<script setup lang="ts">
const emits = defineEmits<{
  close: [];
}>();

const gemeCategoryQry = useQuery({
  equeryKy: [apis.apiGameCategoryList.id],
  queryFn: () => apis.apiGameCategoryList(undefined),
});

const gameTypes = computed(() => gemeCategoryQry.data.value);

const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);
</script>

<template>
  <div class="text-sm text-sys-text-body">
    <div class="mb-4 text-sys-text-head">
      {{ $t('v7FZhX4Rp7gBDgtMbTu6m') }}
    </div>

    <div>
      {{ $t('oSAfZ0G24Usv7RgPvwSue') }}
    </div>
    <div class="grid mt-2 gap-1 rounded-2 bg-sys-layer-a px-3 py-2">
      <div class="col-span-full mb-1 text-sys-text-head">
        {{ $t('xxn29eOmm4RnCEsDl6AbX') }}
      </div>
      <div v-for="item in gameTypes" :key="item.name">
        {{ item.ename }}: {{ $t('an0xB3Kronuhw2ztsiKw3') }} * {{ bignumber(item.coefficient).multipliedBy(Number(userInfo?.vip_level?.rakeback_rate)).multipliedBy(100) }}%
      </div>
      <div>
        {{ $t('7DmKsLnLmDQdc7jWnEgN') }}
      </div>
    </div>

    <div class="divider-h-gradual my-4" />

    <div>
      {{ $t('0mkyelkkltJ72TYwjuR8') }}:
    </div>
    <div class="mt-1 text-sys-text-head">
      {{ $t('73KtgRwSlhL2RxuIto7Pp') }}
    </div>

    <div class="divider-h-gradual my-4" />

    <div>
      {{ $t('c7Dg0fUsfJgSmwukRBr0f') }}:
    </div>
    <div class="mt-2 border border-yellow rounded-2 border-solid p-2">
      <div class="flex items-center text-base text-yellow">
        <i class="i-local:bonuses--start mr-1 text-1.5em -mb-1" />
        <span>{{ $t('n7RpcyLc9SkiPxDj9i0') }}:</span>
      </div>
      <div>
        {{ $t('hvZiSdCXbHLo5PvFai55E') }}
      </div>
    </div>

    <AButton
      class="ant-cover__Button-3d-primary mt-5"
      type="primary" block
      @click="emits('close')"
    >
      {{ $t('pFexkGv2pL4Nl4GmT9ZZm') }}
    </AButton>
  </div>
</template>
