import { createRequest } from '../request';

 const serviceName = 'main.api';

 /**
 * 代理分红历史详情
 */
export const apiActivityAgentBonusDetail = createRequest<
  {
    order_no?: string;
  },
  {
    code: number;
    data: {
      category: string[];
      statistics: {
        category_statistics?: (UserCurrencyStatisticsDetail | null)[];
        currency?: string;
        profit_amount?: string[];
        valuation_amount?: string[];
      }[];
      total: string[];
    };
    msg: string;
    request_id: string;
  }
>("apiActivityAgentBonusDetail", ({ order_no }) => ({
  url: `/v1/activity/agent_bonus/detail`,
  method: "GET",
  params: {
    order_no,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 代理分红历史
 */
export const apiActivityAgentBonusHistory = createRequest<
  {
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
    status?: number;
  },
  {
    code: number;
    data: { list: DaiLiFenHongJiLu[]; pagination: Pagination; total: DaiLiFenHongJiLu };
    msg: string;
    request_id: string;
  }
>("apiActivityAgentBonusHistory", ({ page, page_size, start_time, end_time, status }) => ({
  url: `/v1/activity/agent_bonus/history`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
    status,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 代理分红等级
 */
export const apiActivityAgentBonusLevel = createRequest<
  undefined,
  { code: number; data: YongHuDaiLiDengJiXiangQing[]; msg: string; request_id: string }
>("apiActivityAgentBonusLevel", () => ({
  url: `/v1/activity/agent_bonus/level`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 跳伞奖金领取
 */
export const apiActivityAviatorJackpotClaim = createRequest<
  {
    requestBody?: { rank_type: string };
  },
  {
    code: number;
    data: {
      allow_claim: number;
      currency_balance_list: { balance?: string; currency?: string; logo?: string; name?: string }[];
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityAviatorJackpotClaim", ({ requestBody }) => ({
  url: `/v1/activity/aviator_jackpot/claim`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 跳伞奖金配置
 */
export const apiActivityAviatorJackpotConfig = createRequest<
  undefined,
  {
    code: number;
    data: { income_hourly: string; rules: { id: number; rank: string; reward_rate: string }[] };
    msg: string;
    request_id: string;
  }
>("apiActivityAviatorJackpotConfig", () => ({
  url: `/v1/activity/aviator_jackpot/config`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 跳伞奖金排行榜
 */
export const apiActivityAviatorJackpotRankings = createRequest<
  {
    rank_type?: number;
  },
  {
    code: number;
    data: { history: TiaoSanPaiHangBangTongJi; live: TiaoSanPaiHangBangTongJi };
    msg: string;
    request_id: string;
  }
>("apiActivityAviatorJackpotRankings", ({ rank_type }) => ({
  url: `/v1/activity/aviator_jackpot/rankings`,
  method: "GET",
  params: {
    rank_type,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 活动奖励历史
 */
export const apiActivityBonusesHistory = createRequest<
  undefined,
  { code: number; data: HuoDongJiangLiLiShiXiangQing | null; msg: string; request_id: string }
>("apiActivityBonusesHistory", () => ({
  url: `/v1/activity/bonuses_history`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 活动奖励概览
 */
export const apiActivityBonusesOverview = createRequest<
  undefined,
  {
    code: number;
    data: { regular_bonuses: HuoDongJiangLiXiangQing; special_bonuses: HuoDongJiangLiXiangQing };
    msg: string;
    request_id: string;
  }
>("apiActivityBonusesOverview", () => ({
  url: `/v1/activity/bonuses_overview`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 活动奖励统计
 */
export const apiActivityBonusesStatistics = createRequest<
  undefined,
  {
    code: number;
    data: { month_statistics: HuoDongJiangLiLiShiXiangQing; week_statistics: HuoDongJiangLiLiShiXiangQing };
    msg: string;
    request_id: string;
  }
>("apiActivityBonusesStatistics", () => ({
  url: `/v1/activity/bonuses_statistics`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * Cashback历史
 */
export const apiActivityCashbackHistory = createRequest<
  {
    currency?: string;
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
    type?: number;
  },
  { code: number; data: null | UserCashbackDetail[]; msg: string; pagination: Pagination; request_id: string }
>("apiActivityCashbackHistory", ({ type, start_time, end_time, currency, page, page_size }) => ({
  url: `/v1/activity/cashback/history`,
  method: "GET",
  params: {
    type,
    start_time,
    end_time,
    currency,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 检查活动可领取奖励
 */
export const apiActivityCheckAvailableBonuses = createRequest<
  undefined,
  {
    code: number;
    data: {
      bonus?: ActivityCheckAvailableBonusesDetail;
      cashback?: ActivityCheckAvailableBonusesDetail;
      freespins?: ActivityCheckAvailableBonusesDetail;
      installpwa?: ActivityCheckAvailableBonusesDetail;
      invitespin?: ActivityCheckAvailableBonusesDetail;
      luckyspin?: ActivityCheckAvailableBonusesDetail;
      rakeback?: ActivityCheckAvailableBonusesDetail;
      signup?: ActivityCheckAvailableBonusesDetail;
      upgrade: ActivityCheckAvailableBonusesDetail;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityCheckAvailableBonuses", () => ({
  url: `/v1/activity/check_available_bonuses`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 签到
 */
export const apiActivityDaily = createRequest<
  undefined,
  {
    code: number;
    data: {
      check_in_config: CheckInDetail[];
      check_in_continued_day: number;
      last_time: number;
      remaining_time: number;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityDaily", () => ({ url: `/v1/activity/daily`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 签到奖金领取
 */
export const apiActivityDailyBonusClaim = createRequest<
  {
    days?: number;
  },
  {
    code: number;
    data: {
      allow_claim: number;
      currency_balance_list: { balance: string; currency: string; logo: string; name: string }[];
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityDailyBonusClaim", ({ days }) => ({
  url: `/v1/activity/daily_bonus_claim`,
  method: "POST",
  params: {
    days,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 提交签到
 */
export const apiActivityDailyCheckIn = createRequest<
  undefined,
  {
    code: number;
    data: {
      allow_claim: number;
      currency_balance_list: { balance: string; currency: string; logo: string; name: string }[];
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityDailyCheckIn", () => ({
  url: `/v1/activity/daily_check_in`,
  method: "POST",
  headers: { "Service-Name": serviceName },
}));

/**
 * 存款返佣活动
 */
export const apiActivityDepositCommissions = createRequest<
  undefined,
  { code: number; data: CunKuanFanYongHuoDongXiangQing | null; msg: string; request_id: string }
>("apiActivityDepositCommissions", () => ({
  url: `/v1/activity/deposit_commissions`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 免费旋转详情
 */
export const apiActivityFreeSpins = createRequest<
  undefined,
  { code: number; data: MianFeiXuanZhuanXiangQing; msg: string; request_id: string }
>("apiActivityFreeSpins", () => ({
  url: `/v1/activity/freespins`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 安装PWA活动
 */
export const apiActivityInstallPwa = createRequest<
  undefined,
  {
    code: number;
    data: {
      activity_code: string;
      amount: string;
      available: boolean;
      currency: string;
      ename: string;
      period: number;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityInstallPwa", () => ({
  url: `/v1/activity/installpwa`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 提现历史
 */
export const apiActivityInviteHistory = createRequest<
  undefined,
  { code: number; data: ShiShiTiXianJiLu[]; msg: string; request_id: string }
>("apiActivityInviteHistory", () => ({
  url: `/v1/activity/invite/history`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 抽奖准备
 */
export const apiActivityInviteReady = createRequest<
  {
    is_luckyspin?: number;
  },
  {
    code: number;
    data: { circle: { amount: string; currency: string; type: number }[]; status: YaoQingHuoDongJianChaKeYong };
    msg: string;
    request_id: string;
  }
>("apiActivityInviteReady", ({ is_luckyspin }) => ({
  url: `/v1/activity/invite/ready`,
  method: "GET",
  params: {
    is_luckyspin,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 开始抽奖
 */
export const apiActivityInviteStart = createRequest<
  undefined,
  {
    code: number;
    data: null | {
      amount: string;
      circle_list: null | LuckySpinCircle[];
      expired_time: number;
      order_no: string;
      remaining_count: number;
      result: number;
      withdraw_status: number;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityInviteStart", () => ({
  url: `/v1/activity/invite/start`,
  method: "POST",
  headers: { "Service-Name": serviceName },
}));

/**
 * 邀请用户列表
 */
export const apiActivityInviteUsers = createRequest<
  {
    page?: number;
    page_size?: number;
  },
  {
    code: number;
    data: {
      list: YaoQingGuanXiXiangQing[];
      pagination: { has_more: boolean; page: number; page_size: number; total: number };
      total: number;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityInviteUsers", ({ page, page_size }) => ({
  url: `/v1/activity/invite/users`,
  method: "GET",
  params: {
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 提交提现
 */
export const apiActivityInviteWithdraw = createRequest<
  undefined,
  { code: number; data: {}; msg: string; request_id: string }
>("apiActivityInviteWithdraw", () => ({
  url: `/v1/activity/invite/withdraw`,
  method: "POST",
  headers: { "Service-Name": serviceName },
}));

/**
 * 抽奖准备
 */
export const apiActivityLuckySpinReady = createRequest<
  undefined,
  {
    code: number;
    data: {
      circle: null | LuckySpinCircle[];
      history: null | CurrencyAmountDetail[];
      status: ActivityCheckAvailableBonusesDetail;
    };
    msg: string;
    request_id: string;
  }
>("apiActivityLuckySpinReady", () => ({
  url: `/v1/activity/luckyspin/ready`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 开始抽奖
 */
export const apiActivityLuckySpinStart = createRequest<
  undefined,
  {
    code: number;
    data: null | { circle_list: null | LuckySpinCircle[]; expired_time: number; order_no: string; result: number };
    msg: string;
    request_id: string;
  }
>("apiActivityLuckySpinStart", () => ({
  url: `/v1/activity/luckyspin/start`,
  method: "POST",
  headers: { "Service-Name": serviceName },
}));

/**
 * 欢迎活动列表
 */
export const apiActivityWelcomeBonus = createRequest<
  undefined,
  { code: number; data: HuanYingHuoDongXiangQing[]; msg: string; request_id: string }
>("apiActivityWelcomeBonus", () => ({
  url: `/v1/activity/welcome_bonus`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 代理注册
 */
export const apiAgentSignup = createRequest<
  {
    requestBody?: {
      company_name: string;
      email: string;
      name: string;
      password: string;
      password_confirm: string;
      telegram_username: string;
      traffic_sources: string;
    };
  },
  {}
>("apiAgentSignup", ({ requestBody }) => ({
  url: `/v1/agent/signup`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 忘记密码发送邮件
 */
export const apiAuthForgetPassword = createRequest<
  {
    requestBody?: { cf_turnstile_response: string; email: string; from_type?: number };
  },
  ChengGong
>("apiAuthForgetPassword", ({ requestBody }) => ({
  url: `/v1/auth/forget_password`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 谷歌授权注册/登陆
 */
export const apiAuthGoogleOAuthSignIn = createRequest<
  {
    requestBody?: { code: string; is_luckyspin?: number; redirect_uri: string; referral_code?: string };
  },
  {}
>("apiAuthGoogleOAuthSignIn", ({ requestBody }) => ({
  url: `/v1/auth/google_oauth_sign_in`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 谷歌一键快捷注册/登陆
 */
export const apiAuthGoogleQuickSignIn = createRequest<
  {
    requestBody?: { credential: string; is_luckyspin?: number; referral_code?: string };
  },
  {}
>("apiAuthGoogleQuickSignIn", ({ requestBody }) => ({
  url: `/v1/auth/google_quick_sign_in`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 账号密码登录
 */
export const apiAuthLogin = createRequest<
  {
    requestBody?: { cf_turnstile_response: string; email: string; is_luckyspin?: number; password: string };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiAuthLogin", ({ requestBody }) => ({
  url: `/v1/auth/login`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 账号退出登录
 */
export const apiAuthLogout = createRequest<
  {
    requestBody?: {};
  },
  { code: number; msg: string; request_id: string }
>("apiAuthLogout", ({ requestBody }) => ({
  url: `/v1/auth/logout`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/x-www-form-urlencoded", "Service-Name": serviceName },
}));

/**
 * 账号注册
 */
export const apiAuthRegister = createRequest<
  {
    requestBody?: {
      cf_turnstile_response: string;
      email: string;
      "is_luckyspin  "?: number;
      password: string;
      password_confirm: string;
      referral_code?: string;
    };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiAuthRegister", ({ requestBody }) => ({
  url: `/v1/auth/register`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 注册验证
 */
export const apiAuthRegisterVerify = createRequest<
  {
    requestBody?: { email: string; is_luckyspin?: number; verify_code: string };
  },
  ChengGong
>("apiAuthRegisterVerify", ({ requestBody }) => ({
  url: `/v1/auth/register_verify`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 忘记后重设密码
 */
export const apiAuthResetPassword = createRequest<
  {
    requestBody?: {
      cf_turnstile_response: string;
      email: string;
      password: string;
      password_confirm: string;
      verify_code: string;
    };
  },
  ChengGong
>("apiAuthResetPassword", ({ requestBody }) => ({
  url: `/v1/auth/reset_password`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 再次发送邮箱验证码\n* @deprecated apiAuthSendVerifyEmail
 */
export const apiAuthSendVerifyEmail = createRequest<
  {
    requestBody?: { email: string; type: number };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiAuthSendVerifyEmail", ({ requestBody }) => ({
  url: `/v1/auth/verify_email`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 直接修改密码
 */
export const apiAuthUpdatePassword = createRequest<
  {
    requestBody?: { password: string; password_confirm: string; password_old: string };
  },
  ChengGong
>("apiAuthUpdatePassword", ({ requestBody }) => ({
  url: `/v1/auth/update_password`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 获取二维码URL
 */
export const apiAuthenticatorQrCode = createRequest<
  undefined,
  { code: number; data: { qr_code: string; secret_key: string }; msg: string; request_id: string }
>("apiAuthenticatorQrCode", () => ({
  url: `/v1/authenticator/qrcode`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 验证开关
 */
export const apiAuthenticatorSwitch = createRequest<
  {
    requestBody?: { enable_authenticator: number; password: string; security_code: string };
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiAuthenticatorSwitch", ({ requestBody }) => ({
  url: `/v1/authenticator/switch`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 验证安全码
 */
export const apiAuthenticatorVerifyCode = createRequest<
  {
    requestBody?: { security_code: string };
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiAuthenticatorVerifyCode", ({ requestBody }) => ({
  url: `/v1/authenticator/verify`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 绑定钱包地址
 */
export const apiBindTronAddress = createRequest<
  {
    requestBody?: { trx_address: string };
  },
  { code: number; msg: string; request_id: string }
>("apiBindTronAddress", ({ requestBody }) => ({
  url: `/v1/user/bindtronaddress`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/x-www-form-urlencoded", "Service-Name": serviceName },
}));

/**
 * 获取验证码
 */
export const apiCaptchaGet = createRequest<
  {
    type?: string;
  },
  { code: number; data: { data: string; id: string }; msg: string; request_id: string }
>("apiCaptchaGet", ({ type }) => ({
  url: `/v1/captcha/get`,
  method: "GET",
  params: {
    type,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 佣金记录列表
 */
export const apiCommissionRecord = createRequest<
  {
    currency?: string;
    end_time?: number;
    page?: number;
    page_size?: number;
    referee?: string;
    start_time?: number;
    status?: number;
    type?: number;
  },
  {
    code: number;
    data: null | { list: null | CommissionLogDetail[]; pagination: Pagination; total: CommissionLogDetail };
    msg: string;
    request_id: string;
  }
>("apiCommissionRecord", ({ page, page_size, start_time, end_time, status, currency, referee, type }) => ({
  url: `/v1/activity/commissions/history`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
    status,
    currency,
    referee,
    type,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 币种索引
 */
export const apiCurrencyIndex = createRequest<
  undefined,
  {
    code: number;
    data: null | { crypto: null | CurrencyInfo[]; fiat: null | CurrencyInfo[] };
    msg: string;
    request_id: string;
  }
>("apiCurrencyIndex", () => ({ url: `/v1/currency/index`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 币种USD汇率
 */
export const apiCurrencyUsdRate = createRequest<
  undefined,
  { code: number; data: null | {}; msg: string; request_id: string }
>("apiCurrencyUsdRate", () => ({
  url: `/v1/currency/usd_rate`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 存款返佣记录列表
 */
export const apiDepositCommissionRecord = createRequest<
  {
    currency?: string;
    end_time?: number;
    page?: number;
    page_size?: number;
    referee?: string;
    start_time?: number;
    status?: number;
  },
  {
    code: number;
    data: null | {
      list: null | DepositCommissionLogDetail[];
      pagination: Pagination;
      total: DepositCommissionLogDetail;
    };
    msg: string;
    request_id: string;
  }
>("apiDepositCommissionRecord", ({ page, page_size, start_time, end_time, status, currency, referee }) => ({
  url: `/v1/activity/deposit_commissions/history`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
    status,
    currency,
    referee,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 再次发送邮箱验证码(新)
 */
export const apiEmailSendVerify = createRequest<
  {
    requestBody?: { cf_turnstile_response: string; email: string; type: number };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiEmailSendVerify", ({ requestBody }) => ({
  url: `/v1/email/send_verify`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 验证邮箱验证码(整合)
 */
export const apiEmailVerify = createRequest<
  {
    requestBody?: { email: string; type: number; verify_code: string };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiEmailVerify", ({ requestBody }) => ({
  url: `/v1/email/verify`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 提交反馈
 */
export const apiFeedbackCommit = createRequest<
  {
    requestBody?: { content: string; subject_id: number };
  },
  {}
>("apiFeedbackCommit", ({ requestBody }) => ({
  url: `/v1/feedback/commit`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 反馈主题列表
 */
export const apiFeedbackSubjects = createRequest<
  undefined,
  {
    code: number;
    data: { lang: string; subjects: { subject?: string; subject_id?: number }[] };
    msg: string;
    request_id: string;
  }
>("apiFeedbackSubjects", () => ({
  url: `/v1/feedback/subjects`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 忘记密码验证
 */
export const apiForgetPasswordVerify = createRequest<
  {
    requestBody?: { email: string; verify_code: string };
  },
  ChengGong
>("apiForgetPasswordVerify", ({ requestBody }) => ({
  url: `/v1/auth/forget_password_verify`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 存款奖金领取
 */
export const apiFundDepositBonusClaim = createRequest<
  {
    requestBody?: {};
  },
  {
    code: number;
    data: {
      allow_claim: number;
      currency_balance_list: { balance: string; currency: string; logo: string; name: string }[];
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiFundDepositBonusClaim", ({ requestBody }) => ({
  url: `/v1/fund/deposit/bonus_claim`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 修改存款活动
 */
export const apiFundDepositChangeActivity = createRequest<
  {
    requestBody?: { activity_code: string };
    uid?: string;
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiFundDepositChangeActivity", ({ uid, requestBody }) => ({
  url: `/v1/fund/deposit/change_activity`,
  method: "POST",
  data: requestBody,
  params: {
    uid,
  },
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 获取存款钱包地址
 */
export const apiFundDepositGetAddress = createRequest<
  {
    requestBody?: { chain: string; currency: string };
  },
  { code: number; data: null | UserWalletDetail1; msg: string; request_id: string }
>("apiFundDepositGetAddress", ({ requestBody }) => ({
  url: `/v1/fund/deposit/get_address`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 获取币种网络配置（待定）
 */
export const apiFundDepositGetCurrency = createRequest<
  {
    currency?: string;
  },
  { code: number; data: { list: null | CurrencyTokenDetail[] }; msg: string; request_id: string }
>("apiFundDepositGetCurrency", ({ currency }) => ({
  url: `/v1/fund/deposit/get_currency`,
  method: "GET",
  params: {
    currency,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 付款方式列表
 */
export const apiFundDepositPaymentMethod = createRequest<
  {
    currency?: string;
  },
  {
    code: number;
    data: { bank: FuKuanFangShiXiangQing[]; recommend: FuKuanFangShiXiangQing[]; wallet: FuKuanFangShiXiangQing[] };
    msg: string;
    request_id: string;
  }
>("apiFundDepositPaymentMethod", ({ currency }) => ({
  url: `/v1/fund/deposit/payment_method`,
  method: "GET",
  params: {
    currency,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 查询存款记录
 */
export const apiFundDepositQuery = createRequest<
  {
    order_no: string;
  },
  { code: number; data: DepositDetail | null; msg: string; request_id: string }
>("apiFundDepositQuery", ({ order_no }) => ({
  url: `/v1/fund/deposit/query`,
  method: "GET",
  params: {
    order_no,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 存款准备
 */
export const apiFundDepositReady = createRequest<
  undefined,
  {
    code: number;
    data: { allow_deposit: number; currency_balance_list: CurrencyBalance[]; tips: string };
    msg: string;
    request_id: string;
  }
>("apiFundDepositReady", () => ({
  url: `/v1/fund/deposit/ready`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 存款准备(全)
 */
export const apiFundDepositReadyAll = createRequest<
  {
    currency?: string;
  },
  {
    code: number;
    data: {
      allow_deposit: number;
      currency_balance_list: CurrencyBalance[];
      deposit_and_bet_rate: number;
      install_pwa_bonus?: AnZhuangPwaHuoDongXiangQing;
      signup_bonus?: ZhuCeHuoDongXiangQing;
      tips: string;
      welcome_bonus?: HuanYingHuoDongXiangQing;
      without_bonus?: BuShiYongJiangJinXiangQing;
    };
    msg: string;
    request_id: string;
  }
>("apiFundDepositReadyAll", ({ currency }) => ({
  url: `/v1/fund/deposit/readyall`,
  method: "GET",
  params: {
    currency,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 存款记录列表
 */
export const apiFundDepositRecord = createRequest<
  {
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
  },
  {
    code: number;
    data?: null | { list: null | DepositDetail[]; pagination: Pagination };
    msg: string;
    request_id: string;
  }
>("apiFundDepositRecord", ({ page, page_size, start_time, end_time }) => ({
  url: `/v1/fund/deposit/record`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 放弃滚存活动
 */
export const apiFundDepositScrollCancel = createRequest<
  {
    requestBody?: { order_no: string };
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiFundDepositScrollCancel", ({ requestBody }) => ({
  url: `/v1/fund/deposit/scroll/cancel`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 滚存记录详情
 */
export const apiFundDepositScrollDetail = createRequest<
  {
    order_no?: string;
  },
  { code: number; data: GunCunJiLu | null; msg: string; request_id: string }
>("apiFundDepositScrollDetail", ({ order_no }) => ({
  url: `/v1/fund/deposit/scroll/detail`,
  method: "GET",
  params: {
    order_no,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 滚存记录列表
 */
export const apiFundDepositScrollRecord = createRequest<
  {
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
    status?: number;
    type?: number;
  },
  { code: number; data: { list: GunCunJiLu[]; pagination: Pagination }; msg: string; request_id: string }
>("apiFundDepositScrollRecord", ({ page, page_size, start_time, end_time, type, status }) => ({
  url: `/v1/fund/deposit/scroll/record`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
    type,
    status,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 存款提交
 */
export const apiFundDepositSubmit = createRequest<
  {
    requestBody?: {
      amount: number;
      bank_card_name?: string;
      bank_card_no: string;
      bank_code: string;
      code: string;
      cpf: string;
      currency: string;
      email?: string;
      phone?: string;
    };
  },
  {
    code: number;
    data: { create_time: number; data: string; expire_second: number; order_no: string; type: string };
    msg: string;
    request_id: string;
  }
>("apiFundDepositSubmit", ({ requestBody }) => ({
  url: `/v1/fund/deposit/submit`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 转帐准备
 */
export const apiFundTransferReady = createRequest<
  {
    target_uid: string;
  },
  {
    code: number;
    data: { allow_transfer: number; currency_balance: CurrencyBalance; tips: string; to_user: UserDetail };
    msg: string;
    request_id: string;
  }
>("apiFundTransferReady", ({ target_uid }) => ({
  url: `/v1/fund/transfer/ready`,
  method: "GET",
  params: {
    target_uid,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 转帐记录列表
 */
export const apiFundTransferRecord = createRequest<
  {
    account?: string;
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
  },
  { code: number; data: { list: ZhuanZhangJiLu[]; pagination: Pagination }; msg: string; request_id: string }
>("apiFundTransferRecord", ({ page, page_size, start_time, end_time, account }) => ({
  url: `/v1/fund/transfer/record`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
    account,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 提交转帐
 */
export const apiFundTransgerSubmit = createRequest<
  {
    requestBody?: { amount: number; security_code: string; target_uid: string };
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiFundTransgerSubmit", ({ requestBody }) => ({
  url: `/v1/fund/transfer/submit`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 提款请求(法币)
 */
export const apiFundWithdrawFaitSubmit = createRequest<
  {
    requestBody?: {
      amount: string;
      bank_card_account: string;
      bank_card_name: string;
      bank_code: string;
      bank_ifsc_code: string;
      code: string;
      cpf: string;
      currency: string;
      id_card?: string;
      id_type?: string;
      security_code: string;
    };
  },
  {
    code: number;
    data?: { address: string; amount: string; currency: CurrencyTokenDetail; remark: string };
    msg: string;
    request_id: string;
  }
>("apiFundWithdrawFaitSubmit", ({ requestBody }) => ({
  url: `/v1/fund/withdraw/fait_submit`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 提款方式列表
 */
export const apiFundWithdrawPaymentMethod = createRequest<
  {
    currency?: string;
  },
  {
    code: number;
    data: { bank?: TiKuanFangShiXiangQing[]; wallet: TiKuanFangShiXiangQing[] };
    msg: string;
    request_id: string;
  }
>("apiFundWithdrawPaymentMethod", ({ currency }) => ({
  url: `/v1/fund/withdraw/payment_method`,
  method: "GET",
  params: {
    currency,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 提款准备
 */
export const apiFundWithdrawReady = createRequest<
  undefined,
  {
    code: number;
    data: {
      allow_withdraw: number;
      currency_balance_list: CurrencyBalance[];
      deposit_and_bet_rate: number;
      disable_msg?: string;
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiFundWithdrawReady", () => ({
  url: `/v1/fund/withdraw/ready`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 提款准备(全)
 */
export const apiFundWithdrawReadyAll = createRequest<
  {
    currency?: string;
  },
  {
    code: number;
    data: {
      allow_withdraw: number;
      currency_balance_list: CurrencyBalance[];
      deposit_and_bet_rate: number;
      disable_msg?: string;
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiFundWithdrawReadyAll", ({ currency }) => ({
  url: `/v1/fund/withdraw/readyall`,
  method: "GET",
  params: {
    currency,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 提款记录列表
 */
export const apiFundWithdrawRecord = createRequest<
  {
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
  },
  {
    code: number;
    data?: null | { list: null | WithdrawDetail[]; pagination: Pagination };
    msg: string;
    request_id: string;
  }
>("apiFundWithdrawRecord", ({ page, page_size, start_time, end_time }) => ({
  url: `/v1/fund/withdraw/record`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 提款请求
 */
export const apiFundWithdrawSubmit = createRequest<
  {
    requestBody?: { address: string; amount: string; chain: string; currency: string; security_code: string };
  },
  {
    code: number;
    data?: { address: string; amount: string; currency: CurrencyTokenDetail; remark: string };
    msg: string;
    request_id: string;
  }
>("apiFundWithdrawSubmit", ({ requestBody }) => ({
  url: `/v1/fund/withdraw/submit`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 全部游戏
 */
export const apiGameAll = createRequest<
  undefined,
  { code: number; data: { game_list: GameTypeDetail[]; game_map?: {} }; msg: string; request_id: string }
>("apiGameAll", () => ({ url: `/v1/game/all`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 返回游戏大厅(三方游戏)
 */
export const apiGameBack = createRequest<undefined, {}>("apiGameBack", () => ({
  url: `/v1/game/back`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏分类列表
 */
export const apiGameCategoryList = createRequest<
  undefined,
  { code: number; data: GameCategoryDetail[]; msg: string; request_id: string }
>("apiGameCategoryList", () => ({
  url: `/v1/game/category_list`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏详情
 */
export const apiGameDetail = createRequest<
  {
    code: string;
  },
  { code: number; data: null | GameDetail; msg: string; request_id: string }
>("apiGameDetail", ({ code }) => ({
  url: `/v1/game/detail`,
  method: "GET",
  params: {
    code,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 喜欢/取消喜欢游戏
 */
export const apiGameLike = createRequest<
  {
    requestBody?: { game_code: string };
  },
  { code: number; data: { count: number; result: number }; msg: string; request_id: string }
>("apiGameLike", ({ requestBody }) => ({
  url: `/v1/game/like`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 喜欢的游戏列表
 */
export const apiGameLikeList = createRequest<
  {
    keyword?: string;
    page?: number;
    page_size?: number;
    provider_code?: string;
    sort?: number;
    type_id?: number;
  },
  { code: number; data: null | { list: null | GameDetail[]; pagination: Pagination }; msg: string; request_id: string }
>("apiGameLikeList", ({ type_id, provider_code, keyword, sort, page, page_size }) => ({
  url: `/v1/game/like_list`,
  method: "GET",
  params: {
    type_id,
    provider_code,
    keyword,
    sort,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏列表
 */
export const apiGameList = createRequest<
  {
    keyword?: string;
    page?: number;
    page_size?: number;
    provider_code?: string;
    sort?: number;
    type_code?: string;
  },
  { code: number; data: null | { list: null | GameDetail[]; pagination: Pagination }; msg: string; request_id: string }
>("apiGameList", ({ type_code, provider_code, keyword, sort, page, page_size }) => ({
  url: `/v1/game/list`,
  method: "GET",
  params: {
    type_code,
    provider_code,
    keyword,
    sort,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏记录详情
 */
export const apiGameLogDetail = createRequest<
  {
    game_code?: string;
    order_no?: string;
  },
  {
    code: number;
    data?:
      | null
      | CrashBetLog
      | PlinkoBetLog
      | TowerBetLog
      | ClassicDiceBetLog
      | HashDiceBetLog
      | LimboBetLog
      | MinesBetLog
      | UltimateDiceBetLog
      | WheelBetLog
      | JlYongHuYouXiJiLu
      | UserGameLog;
    msg: string;
    request_id: string;
  }
>("apiGameLogDetail", ({ game_code, order_no }) => ({
  url: `/v1/game/game_log`,
  method: "GET",
  params: {
    game_code,
    order_no,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏记录
 */
export const apiGameLogList = createRequest<
  {
    currency?: string;
    end_time?: number;
    game_code?: string;
    page?: number;
    page_size?: number;
    start_time?: number;
    type_alias?: string;
  },
  {
    code: number;
    data: null | {
      list:
        | null
        | WheelBetLog[]
        | UltimateDiceBetLog[]
        | MinesBetLog[]
        | LimboBetLog[]
        | HashDiceBetLog[]
        | ClassicDiceBetLog[]
        | TowerBetLog[]
        | PlinkoBetLog[]
        | CrashBetLog[]
        | VideoPokerYouXiJiLu[]
        | UserGameLog[];
      pagination: Pagination;
    };
    msg: string;
    request_id: string;
  }
>("apiGameLogList", ({ currency, game_code, type_alias, start_time, end_time, page, page_size }) => ({
  url: `/v1/game/history`,
  method: "GET",
  params: {
    currency,
    game_code,
    type_alias,
    start_time,
    end_time,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 大厅游戏记录(首页)
 */
export const apiGameLogLobby = createRequest<
  {
    page?: number;
    page_size?: number;
  },
  {
    code: number;
    data: null | { list: null | LobbyBetLogDetail[]; pagination: Pagination };
    msg: string;
    request_id: string;
  }
>("apiGameLogLobby", ({ page, page_size }) => ({
  url: `/v1/game/lobby`,
  method: "GET",
  params: {
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏供应商列表
 */
export const apiGameProviderList = createRequest<
  {
    page?: number;
    page_size?: number;
  },
  { code: number; data: { list: GameProviderDetail[]; pagination: Pagination }; msg: string; request_id: string }
>("apiGameProviderList", ({ page, page_size }) => ({
  url: `/v1/game/provider_list`,
  method: "GET",
  params: {
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 随机游戏列表
 */
export const apiGameRandomList = createRequest<
  undefined,
  { code: number; data: { game_list: GameTypeDetail[]; game_map?: {} }; msg: string; request_id: string }
>("apiGameRandomList", () => ({
  url: `/v1/game/random_list`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏统计明细
 */
export const apiGameStatosticDetails = createRequest<
  {
    code?: string;
    id: string;
    type_alias?: string;
  },
  { code: number; data: UserGameStatustics[]; msg: string; request_id: string }
>("apiGameStatosticDetails", ({ id, code, type_alias }) => ({
  url: `/v1/game/statistics_details`,
  method: "GET",
  params: {
    id,
    code,
    type_alias,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏类型详情
 */
export const apiGameTypeDetail = createRequest<
  {
    alias?: string;
  },
  { code: number; data: null | GameTypeDetail; msg: string; request_id: string }
>("apiGameTypeDetail", ({ alias }) => ({
  url: `/v1/game/type_detail`,
  method: "GET",
  params: {
    alias,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 游戏类型选择
 */
export const apiGameTypeSelect = createRequest<
  undefined,
  { code: number; data: GameTypeDetail[]; msg: string; request_id: string }
>("apiGameTypeSelect", () => ({
  url: `/v1/game/type_select`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 启动原创游戏
 */
export const apiGametLaunch = createRequest<
  {
    game_code?: string;
  },
  { code: number; data: { domain: string; game_token?: string }; msg: string; request_id: string }
>("apiGametLaunch", ({ game_code }) => ({
  url: `/v1/game/launch`,
  method: "GET",
  params: {
    game_code,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * Manifest
 */
export const apiHomeManifest = createRequest<
  {
    source?: string;
    token?: string;
  },
  TonManifest
>("apiHomeManifest", ({ source, token }) => ({
  url: `/v1/home/<USER>
  method: "GET",
  params: {
    source,
    token,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 握手协议
 */
export const apiInitHandshake = createRequest<
  undefined,
  { code: number; data: HandshakeDetail; msg: string; request_id: string }
>("apiInitHandshake", () => ({ url: `/v1/init/handshake`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * TonManifest
 */
export const apiInitTonManifest = createRequest<undefined, TonManifest>("apiInitTonManifest", () => ({
  url: `/v1/init/ton_manifest`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 添加渠道
 */
export const apiInviteAddChannel = createRequest<
  {
    requestBody?: {
      commissions_shares?: { category_id: number; game_id: number; shares: number }[];
      is_default: number;
      name: string;
      referral_code: string;
    };
  },
  { code: number; data: null | UserInviteChannelDetail; msg: string; request_id: string }
>("apiInviteAddChannel", ({ requestBody }) => ({
  url: `/v1/invite/add_channel`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 推荐奖金规则列表
 */
export const apiInviteBonusRules = createRequest<
  undefined,
  { code: number; data: TuiJianJiangJinGuiZeXiangQing[]; msg: string; request_id: string }
>("apiInviteBonusRules", () => ({
  url: `/v1/invite/bonus_rules`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 我的奖金领取
 */
export const apiInviteBonusesClaim = createRequest<
  {
    requestBody?: { type: number };
  },
  {
    code: number;
    data: {
      allow_claim: number;
      currency_balance_list: { balance: string; currency: string; logo: string; name: string }[];
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiInviteBonusesClaim", ({ requestBody }) => ({
  url: `/v1/invite/bonuses/claim`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 我的奖金余额
 */
export const apiInviteBonusesCurrency = createRequest<
  {
    type?: number;
  },
  {
    code: number;
    data: {
      allow_claim: number;
      currency_balance_list: { balance: string; currency: string; logo: string; name: string }[];
      tips: string;
    };
    msg: string;
    request_id: string;
  }
>("apiInviteBonusesCurrency", ({ type }) => ({
  url: `/v1/invite/bonuses/currency`,
  method: "GET",
  params: {
    type,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 渠道管理
 */
export const apiInviteChannel = createRequest<
  {
    end_time: number;
    page?: number;
    page_size?: number;
    start_time: number;
  },
  {
    code: number;
    data: null | { list: null | UserInviteChannelDetail[]; pagination: Pagination };
    msg: string;
    request_id: string;
  }
>("apiInviteChannel", ({ page, page_size, start_time, end_time }) => ({
  url: `/v1/invite/channel`,
  method: "GET",
  params: {
    page,
    page_size,
    start_time,
    end_time,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 渠道详情
 */
export const apiInviteChannelDetail = createRequest<
  {
    channel_id: string;
  },
  { code: number; data: null | UserInviteChannelDetail; msg: string; request_id: string }
>("apiInviteChannelDetail", ({ channel_id }) => ({
  url: `/v1/invite/channel_detail`,
  method: "GET",
  params: {
    channel_id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 删除渠道
 */
export const apiInviteDeleteChannel = createRequest<
  {
    requestBody?: { channel_id: string };
  },
  { code: number; data?: null | {}; msg: string; request_id: string }
>("apiInviteDeleteChannel", ({ requestBody }) => ({
  url: `/v1/invite/delete_channel`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 实时奖金
 */
export const apiInviteLiveRewards = createRequest<
  undefined,
  {
    code: number;
    data: {
      leaderboard: { avatar: number; name: string; rank: number }[];
      rewards_list: { amount: string; avatar: number; level: number; name: string; uid: string }[];
      total_referrals: number;
      total_rewards: string;
    };
    msg: string;
    request_id: string;
  }
>("apiInviteLiveRewards", () => ({
  url: `/v1/invite/live_rewards`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * 我的收益曲线
 */
export const apiInviteProfitChartLine = createRequest<
  {
    channel_id?: string;
    time_type?: number;
  },
  {
    code: number;
    data: { axis_data: number[]; name: string; profit_amount: string[]; timeline: string; total_profit_amount: string };
    msg: string;
    request_id: string;
  }
>("apiInviteProfitChartLine", ({ time_type, channel_id }) => ({
  url: `/v1/invite/profitChartLine`,
  method: "GET",
  params: {
    time_type,
    channel_id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 我的推荐曲线
 */
export const apiInviteReferralsChartLine = createRequest<
  {
    channel_id?: string;
    time_type?: number;
  },
  {
    code: number;
    data: {
      axis_data: string[];
      deposit_count: number[];
      name: string;
      reg_count: number[];
      timeline: string;
      total_deposit_count: number;
      total_reg_count: number;
    };
    msg: string;
    request_id: string;
  }
>("apiInviteReferralsChartLine", ({ time_type, channel_id }) => ({
  url: `/v1/invite/referralsChartLine`,
  method: "GET",
  params: {
    time_type,
    channel_id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 我的奖金历史
 */
export const apiInviteRewardsHistory = createRequest<
  {
    cursor?: number;
  },
  {
    code: number;
    data: {
      list?: {
        amount: string;
        create_time: number;
        log_id: number;
        rewards_detail: { amount: string; currency: string; logo: string };
        type: number;
      }[];
      pagination: Pagination;
    };
    msg: string;
    request_id: string;
  }
>("apiInviteRewardsHistory", ({ cursor }) => ({
  url: `/v1/invite/rewards_history`,
  method: "GET",
  params: {
    cursor,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 邀请概览统计
 */
export const apiInviteStatistics = createRequest<
  {
    end_time?: number;
    start_time?: number;
  },
  { code: number; data: TuiJianGaiLanTongJiXiangQing; msg: string; request_id: string }
>("apiInviteStatistics", ({ start_time, end_time }) => ({
  url: `/v1/invite/statistics`,
  method: "GET",
  params: {
    start_time,
    end_time,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 编辑渠道
 */
export const apiInviteUpdateChannel = createRequest<
  {
    requestBody?: {
      channel_id: string;
      commissions_shares?: { category_id: number; game_id: number; shares: number }[];
      is_default?: number;
      name: string;
    };
  },
  { code: number; data: null | UserInviteChannelDetail; msg: string; request_id: string }
>("apiInviteUpdateChannel", ({ requestBody }) => ({
  url: `/v1/invite/update_channel`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 编辑我邀请的用户返佣率
 */
export const apiInviteUpdateUserCommissionsRates = createRequest<
  {
    requestBody?: { commissions_rates: { category_id: number; commissions: number; game_id: number }[]; id: string };
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiInviteUpdateUserCommissionsRates", ({ requestBody }) => ({
  url: `/v1/invite/update_user_commissions_rates`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 我邀请的用户返佣率列表
 */
export const apiInviteUserCommissionsRates = createRequest<
  {
    id: string;
  },
  { code: number; data?: YongHuFanYongShuaiXiangQing[]; msg: string; request_id: string }
>("apiInviteUserCommissionsRates", ({ id }) => ({
  url: `/v1/invite/user_commissions_rates`,
  method: "GET",
  params: {
    id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 我邀请的用户列表
 */
export const apiInviteUsers = createRequest<
  {
    channel_id?: string;
    end_time?: number;
    name?: string;
    page?: number;
    page_size?: number;
    start_time?: number;
  },
  {
    code: number;
    data: null | { list: null | ReferralUserDetail[]; pagination: Pagination; path: null | UserDetailLite[] };
    msg: string;
    request_id: string;
  }
>("apiInviteUsers", ({ channel_id, start_time, end_time, page, page_size, name }) => ({
  url: `/v1/invite/users`,
  method: "GET",
  params: {
    channel_id,
    start_time,
    end_time,
    page,
    page_size,
    name,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 头像列表
 */
export const apiLevelAvatar = createRequest<
  undefined,
  { code: number; data: null | AvatarLevelDetail[]; msg: string; request_id: string }
>("apiLevelAvatar", () => ({ url: `/v1/level/avatar`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 领取升级奖励
 */
export const apiLevelGetUpgradeReward = createRequest<
  {
    requestBody?: { level: number };
  },
  {}
>("apiLevelGetUpgradeReward", ({ requestBody }) => ({
  url: `/v1/level/get_upgrade_reward`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 等级信息
 */
export const apiLevelInfo = createRequest<
  undefined,
  { code: number; data: null | UserVipLevelDetail[]; msg: string; request_id: string }
>("apiLevelInfo", () => ({ url: `/v1/level/info`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 检查Token
 */
export const apiNoticeFcmCheckToken = createRequest<undefined, {}>("apiNoticeFcmCheckToken", () => ({
  url: `/v1/notice/fcm/check_token`,
  method: "GET",
  headers: { "Service-Name": serviceName },
}));

/**
 * Fcm设备订阅
 */
export const apiNoticeFcmSubcription = createRequest<
  {
    requestBody?: { token: string };
  },
  {}
>("apiNoticeFcmSubcription", ({ requestBody }) => ({
  url: `/v1/notice/fcm/subscription`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 个人通知详情
 */
export const apiNoticePersonalDetail = createRequest<
  {
    notice_id: number;
  },
  { code: number; data: GeRenTongZhiXiangQing; msg: string; request_id: string }
>("apiNoticePersonalDetail", ({ notice_id }) => ({
  url: `/v1/notice/personal/detail`,
  method: "GET",
  params: {
    notice_id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 个人通知列表
 */
export const apiNoticePersonalLIst = createRequest<
  {
    page?: number;
    page_size?: number;
  },
  { code: number; data: { list: GeRenTongZhiXiangQing[]; pagination: Pagination }; msg: string; request_id: string }
>("apiNoticePersonalLIst", ({ page, page_size }) => ({
  url: `/v1/notice/personal/list`,
  method: "GET",
  params: {
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 个人通知一键阅读
 */
export const apiNoticePersonalRead = createRequest<
  {
    notice_id: number;
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiNoticePersonalRead", ({ notice_id }) => ({
  url: `/v1/notice/personal/read`,
  method: "POST",
  params: {
    notice_id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 平台通知详情
 */
export const apiNoticePlatformDetail = createRequest<
  {
    notice_id: number;
  },
  { code: number; data: PingTaiTongZhiXiangQing; msg: string; request_id: string }
>("apiNoticePlatformDetail", ({ notice_id }) => ({
  url: `/v1/notice/platform/detail`,
  method: "GET",
  params: {
    notice_id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 平台通知列表
 */
export const apiNoticePlatformLIst = createRequest<
  {
    page?: number;
    page_size?: number;
  },
  { code: number; data: { list: PingTaiTongZhiXiangQing[]; pagination: Pagination }; msg: string; request_id: string }
>("apiNoticePlatformLIst", ({ page, page_size }) => ({
  url: `/v1/notice/platform/list`,
  method: "GET",
  params: {
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 平台通知一键阅读
 */
export const apiNoticePlatformReadAll = createRequest<
  {
    notice_id?: number;
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiNoticePlatformReadAll", ({ notice_id }) => ({
  url: `/v1/notice/platform/readall`,
  method: "POST",
  params: {
    notice_id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 注册订阅设备
 */
export const apiNoticeSubscriptionsRegister = createRequest<
  {
    requestBody?: { auth: string; endpoint: string; p256dh: string };
  },
  {}
>("apiNoticeSubscriptionsRegister", ({ requestBody }) => ({
  url: `/v1/notice/subscriptions/register`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 促销活动列表
 */
export const apiPromoList = createRequest<
  {
    page?: number;
    page_size?: number;
    type?: number;
  },
  {
    code: number;
    data: {
      list: CuXiaoHuoDongXiangQing[];
      pagination: { has_more: boolean; page: number; page_size: number; total: number };
    };
    msg: string;
    request_id: string;
  }
>("apiPromoList", ({ page, page_size, type }) => ({
  url: `/v1/promo/list`,
  method: "GET",
  params: {
    page,
    page_size,
    type,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 分享站点
 */
export const apiShare = createRequest<
  {
    game_code?: string;
    ic?: string;
    order_no?: string;
    share_type: string;
  },
  {}
>("apiShare", ({ share_type, ic, order_no, game_code }) => ({
  url: `/v1/share/site`,
  method: "GET",
  params: {
    share_type,
    ic,
    order_no,
    game_code,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 账变明细
 */
export const apiShareFundDetail = createRequest<
  {
    order_no: string;
    share_type: string;
  },
  { code: number; data: null | ZhangBianJiLu; msg: string; request_id: string }
>("apiShareFundDetail", ({ order_no, share_type }) => ({
  url: `/v1/share/fund/detail`,
  method: "GET",
  params: {
    order_no,
    share_type,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 发送分享
 */
export const apiShareSend = createRequest<
  {
    requestBody?: { order_no: string; share_type: string };
  },
  { code: number; data: null; msg: string; request_id: string }
>("apiShareSend", ({ requestBody }) => ({
  url: `/v1/share/send`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 获取Slot配置
 */
export const apiSlotGetSlotSettings = createRequest<
  {
    game_code: string;
  },
  { code: number; data: null | SlotPeiZhi; msg: string; request_id: string }
>("apiSlotGetSlotSettings", ({ game_code }) => ({
  url: `/v1/slot/get_slot_settings`,
  method: "GET",
  params: {
    game_code,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 启动游戏
 */
export const apiSlotLaunchGame = createRequest<
  {
    bet_currency?: string;
    game_code?: string;
    is_try?: number;
    show_currency?: string;
  },
  { code: number; data: { data: string; type: string }; msg: string; request_id: string }
>("apiSlotLaunchGame", ({ game_code, bet_currency, show_currency, is_try }) => ({
  url: `/v1/slot/launch_game`,
  method: "GET",
  params: {
    game_code,
    bet_currency,
    show_currency,
    is_try,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 启动游戏
 */
export const apiSportLaunchGame = createRequest<
  {
    bet_currency: string;
    game_code?: string;
  },
  {}
>("apiSportLaunchGame", ({ bet_currency, game_code }) => ({
  url: `/v1/sport/launch_game`,
  method: "GET",
  params: {
    bet_currency,
    game_code,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 团队成员账变
 */
export const apiTeamFundDetails = createRequest<
  {
    currency?: string;
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
    target?: string;
    type?: number;
  },
  {
    code: number;
    data?: null | { list?: ZhangBianJiLu | null; pagination?: Pagination | null };
    msg: string;
    request_id: string;
  }
>("apiTeamFundDetails", ({ type, start_time, end_time, currency, target, page, page_size }) => ({
  url: `/v1/team/fund_details`,
  method: "GET",
  params: {
    type,
    start_time,
    end_time,
    currency,
    target,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 团队游戏记录(单个用户)
 */
export const apiTeamGameLogList = createRequest<
  {
    code?: string;
    currency?: string;
    end_time?: number;
    filter?: number;
    start_time?: number;
    target?: string;
  },
  {
    code: number;
    data?: null | { list: null | UserGameLog[]; pagination: Pagination };
    msg: string;
    request_id: string;
  }
>("apiTeamGameLogList", ({ target, code, start_time, end_time, currency, filter }) => ({
  url: `/v1/team/game_log`,
  method: "GET",
  params: {
    target,
    code,
    start_time,
    end_time,
    currency,
    filter,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 我的团队成员(下级)
 */
export const apiTeamMember = createRequest<
  {
    page?: number;
    page_size?: number;
    target?: string;
  },
  { code: number; data?: null | { list: null | UserDetail[]; pagination: Pagination }; msg: string; request_id: string }
>("apiTeamMember", ({ target, page, page_size }) => ({
  url: `/v1/team/member`,
  method: "GET",
  params: {
    target,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 团队报表
 */
export const apiTeamReport = createRequest<
  {
    category_id?: number;
    currency?: string;
    end_time?: number;
    ignore_null?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
    target?: string;
  },
  {
    code: number;
    data: null | {
      list: null | ReferralUserDetail[];
      pagination: Pagination;
      path: null | UserDetailLite[];
      total?: TeamStatisticsDetail | null;
    };
    msg: string;
    request_id: string;
  }
>("apiTeamReport", ({ target, currency, category_id, ignore_null, start_time, end_time, page, page_size }) => ({
  url: `/v1/team/report`,
  method: "GET",
  params: {
    target,
    currency,
    category_id,
    ignore_null,
    start_time,
    end_time,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 团队人数统计
 */
export const apiTeamStatistics = createRequest<
  undefined,
  {
    code: number;
    data: {
      all_count: number;
      deposit_count: number;
      level_count: number;
      today_ftd_count: number;
      today_reg_count: number;
    };
    msg: string;
    request_id: string;
  }
>("apiTeamStatistics", () => ({ url: `/v1/team/statistics`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 初始化Telegram用户信息
 */
export const apiTelegramCheckUser = createRequest<
  {
    requestBody?: { init_data: string; is_luckyspin?: number; referral_code?: string };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiTelegramCheckUser", ({ requestBody }) => ({
  url: `/v1/telegram/check_user`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 我的TG机器人
 */
export const apiTgBotInfo = createRequest<
  undefined,
  { code: number; data: null | TelegramBotDetail; msg: string; request_id: string }
>("apiTgBotInfo", () => ({ url: `/v1/tg_bot/info`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 开通TG机器人
 */
export const apiTgBotOpen = createRequest<
  {
    requestBody?: {
      security_code?: string;
      telegram_bot_webapp_name: string;
      telegram_cs_username?: string;
      token: string;
    };
  },
  {}
>("apiTgBotOpen", ({ requestBody }) => ({
  url: `/v1/tg_bot/open`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 解绑TG机器人
 */
export const apiTgBotUnbind = createRequest<
  {
    requestBody?: { security_code?: string };
  },
  {}
>("apiTgBotUnbind", ({ requestBody }) => ({
  url: `/v1/tg_bot/unbind`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 编辑TG机器人
 */
export const apiTgBotUpdate = createRequest<
  {
    requestBody?: {
      security_code?: string;
      telegram_bot_enable: number;
      telegram_bot_token?: string;
      telegram_bot_webapp_name: string;
      telegram_cs_username?: string;
    };
  },
  {}
>("apiTgBotUpdate", ({ requestBody }) => ({
  url: `/v1/tg_bot/update`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * TTPAY充值
 */
export const apiTtPayRecharge = createRequest<
  {
    requestBody?: { amount: number };
  },
  { code: number; data?: TtPayOrderResp; msg: string; request_id: string }
>("apiTtPayRecharge", ({ requestBody }) => ({
  url: `/v1/ttpay/recharge`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 获取我的余额
 */
export const apiUserBalance = createRequest<
  undefined,
  { code: number; data: CurrencyBalance[]; msg: string; request_id: string }
>("apiUserBalance", () => ({ url: `/v1/user/balance`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * Metamask钱包绑定-Nonce
 */
export const apiUserBindEthNonce = createRequest<
  {
    requestBody?: { address: string };
  },
  { code: number; data: { nonce: string }; msg: string; request_id: string }
>("apiUserBindEthNonce", ({ requestBody }) => ({
  url: `/v1/user/bindethnonce`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Metamask钱包绑定-绑定
 */
export const apiUserBindEthWallet = createRequest<
  {
    requestBody?: { address: string; fingerprint?: string; signature: string };
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserBindEthWallet", ({ requestBody }) => ({
  url: `/v1/user/bindethwallet`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Telegram绑定
 */
export const apiUserBindTelegram = createRequest<
  {
    requestBody?: {
      auth_date: number;
      first_name: string;
      hash: string;
      id: number;
      last_name: string;
      photo_url: string;
      username: string;
    };
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserBindTelegram", ({ requestBody }) => ({
  url: `/v1/user/bindtelegram`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Ton钱包绑定-Nonce
 */
export const apiUserBindTonNonce = createRequest<
  {
    requestBody?: { address: string };
  },
  { code: number; data: { nonce: string }; msg: string; request_id: string }
>("apiUserBindTonNonce", ({ requestBody }) => ({
  url: `/v1/user/bindtonnonce`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Ton钱包绑定-绑定
 */
export const apiUserBindTonWallet = createRequest<
  {
    requestBody?: {
      address: string;
      network: string;
      proof: {
        domain: { lengthBytes: number; value: string };
        payload: string;
        signature: string;
        state_init: string;
        timestamp: number;
      };
      public_key: string;
    };
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserBindTonWallet", ({ requestBody }) => ({
  url: `/v1/user/bindtonwallet`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Tron钱包绑定-Nonce
 */
export const apiUserBindTronNonce = createRequest<
  {
    requestBody?: { address: string };
  },
  { code: number; data: { nonce: string }; msg: string; request_id: string }
>("apiUserBindTronNonce", ({ requestBody }) => ({
  url: `/v1/user/bindtronnonce`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Tron钱包绑定-绑定
 */
export const apiUserBindTronWallet = createRequest<
  {
    requestBody?: { address: string; fingerprint?: string; signature: string };
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserBindTronWallet", ({ requestBody }) => ({
  url: `/v1/user/bindtronwallet`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 用户详情
 */
export const apiUserDetail = createRequest<
  {
    id?: string;
  },
  { code: number; data: UserDetail; msg: string; request_id: string }
>("apiUserDetail", ({ id }) => ({
  url: `/v1/user/detail`,
  method: "GET",
  params: {
    id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 我的账变
 */
export const apiUserFundDetails = createRequest<
  {
    currency?: string;
    end_time?: number;
    name?: string;
    page?: number;
    page_size?: number;
    start_time?: number;
    type?: number;
  },
  {
    code: number;
    data?: null | { list: null | ZhangBianJiLu[]; pagination: Pagination };
    msg: string;
    request_id: string;
  }
>("apiUserFundDetails", ({ name, type, start_time, end_time, currency, page, page_size }) => ({
  url: `/v1/fund/details`,
  method: "GET",
  params: {
    name,
    type,
    start_time,
    end_time,
    currency,
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 交易历史
 */
export const apiUserFundTransactions = createRequest<
  {
    action: string;
    currency?: string;
    end_time?: number;
    game_code?: string;
    name?: string;
    order_no?: string;
    page?: number;
    page_size?: number;
    start_time?: number;
    type?: number;
    type_alias?: string;
  },
  { code: number; data: { list: ZhangBianJiLu[]; pagination: Pagination }; msg: string; request_id: string }
>(
  "apiUserFundTransactions",
  ({ action, name, type, start_time, end_time, currency, game_code, type_alias, page, page_size, order_no }) => ({
    url: `/v1/fund/transactions`,
    method: "GET",
    params: {
      action,
      name,
      type,
      start_time,
      end_time,
      currency,
      game_code,
      type_alias,
      page,
      page_size,
      order_no,
    },
    headers: { "Service-Name": serviceName },
  }),
);

/**
 * 查看用户各币种游戏数据
 */
export const apiUserGameCurrencyList = createRequest<
  {
    id?: string;
  },
  { code: number; data?: null | UserGameStatustics[]; msg: string; request_id: string }
>("apiUserGameCurrencyList", ({ id }) => ({
  url: `/v1/user/game_currency_list`,
  method: "GET",
  params: {
    id,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 我的信息
 */
export const apiUserInfo = createRequest<
  undefined,
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiUserInfo", () => ({ url: `/v1/user/info`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * 日报表
 */
export const apiUserReportDaily = createRequest<
  {
    currency?: string;
    end_time?: number;
    page?: number;
    page_size?: number;
    start_time?: number;
  },
  {
    code: number;
    data: null | { list: null | UserDailyReportDetail[]; pagination?: Pagination };
    msg: string;
    request_id: string;
  }
>("apiUserReportDaily", ({ start_time, end_time, page, page_size, currency }) => ({
  url: `/v1/user/report/daily`,
  method: "GET",
  params: {
    start_time,
    end_time,
    page,
    page_size,
    currency,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 数据报表概览
 */
export const apiUserReportOverview = createRequest<
  {
    currency?: string;
    end_time?: number;
    start_time?: number;
  },
  {}
>("apiUserReportOverview", ({ start_time, end_time, currency }) => ({
  url: `/v1/user/report/overview`,
  method: "GET",
  params: {
    start_time,
    end_time,
    currency,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 踢人下线
 */
export const apiUserSessionsKick = createRequest<
  {
    requestBody?: { password?: string; security_code?: string; session_ids: string[] };
  },
  { code: number; data: { rows_affected: number }; msg: string; request_id: string }
>("apiUserSessionsKick", ({ requestBody }) => ({
  url: `/v1/user/sessions/kick`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 会话列表
 */
export const apiUserSessionsList = createRequest<
  {
    page?: number;
    page_size?: number;
  },
  {
    code: number;
    data: null | { list: null | UserSessionDetail[]; pagination?: Pagination };
    msg: string;
    request_id: string;
  }
>("apiUserSessionsList", ({ page, page_size }) => ({
  url: `/v1/user/sessions/list`,
  method: "GET",
  params: {
    page,
    page_size,
  },
  headers: { "Service-Name": serviceName },
}));

/**
 * 设置邮箱
 */
export const apiUserSetEmail = createRequest<
  {
    requestBody?: {
      cf_turnstile_response: string;
      email: string;
      email_confirm: string;
      password: string;
      password_confirm: string;
    };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiUserSetEmail", ({ requestBody }) => ({
  url: `/v1/user/setemail`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 设置邮箱验证
 */
export const apiUserSetEmailVerify = createRequest<
  {
    requestBody?: { email: string; verify_code: string };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiUserSetEmailVerify", ({ requestBody }) => ({
  url: `/v1/user/setemailverify`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Metamask钱包解除绑定
 */
export const apiUserUnBindEthWallet = createRequest<
  {
    requestBody?: {};
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserUnBindEthWallet", ({ requestBody }) => ({
  url: `/v1/user/unbindethwallet`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Telegram解除绑定
 */
export const apiUserUnBindTelegram = createRequest<
  {
    requestBody?: {};
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserUnBindTelegram", ({ requestBody }) => ({
  url: `/v1/user/unbindtelegram`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Ton钱包解除绑定
 */
export const apiUserUnBindTonWallet = createRequest<
  {
    requestBody?: {};
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserUnBindTonWallet", ({ requestBody }) => ({
  url: `/v1/user/unbindtonwallet`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Tron钱包解除绑定
 */
export const apiUserUnBindTronWallet = createRequest<
  {
    requestBody?: {};
  },
  { code: number; data: {}; msg: string; request_id: string }
>("apiUserUnBindTronWallet", ({ requestBody }) => ({
  url: `/v1/user/unbindtronwallet`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 设置昵称&头像
 */
export const apiUserUpdateNickname = createRequest<
  {
    requestBody?: { avatar: number; nickname: string };
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiUserUpdateNickname", ({ requestBody }) => ({
  url: `/v1/user/updatenickname`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 设置用户配置
 */
export const apiUserUpdateSetting = createRequest<
  {
    requestBody?: {};
  },
  { code: number; data: MyUserDetail; msg: string; request_id: string }
>("apiUserUpdateSetting", ({ requestBody }) => ({
  url: `/v1/user/update_setting`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 实时提现记录
 */
export const apiWithdrawLive = createRequest<
  undefined,
  { code: number; data: ShiShiTiXianJiLu[]; msg: string; request_id: string }
>("apiWithdrawLive", () => ({ url: `/v1/withdraw/live`, method: "GET", headers: { "Service-Name": serviceName } }));

/**
 * Metamask一键登录-获取Nonce
 */
export const authMetamaskNonce = createRequest<
  {
    requestBody?: { address: string };
  },
  { code: number; data: { nonce: string }; msg: string; request_id: string }
>("authMetamaskNonce", ({ requestBody }) => ({
  url: `/v1/auth/metamask_nonce`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Metamask一键注册/登陆
 */
export const authMetamaskRegisterOrLogin = createRequest<
  {
    requestBody?: {
      address: string;
      fingerprint: string;
      is_luckyspin?: number;
      referral_code?: string;
      signature: string;
    };
  },
  {}
>("authMetamaskRegisterOrLogin", ({ requestBody }) => ({
  url: `/v1/auth/metamask_login`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Telegram一键注册/登陆
 */
export const authTelegramRegisterOrLogin = createRequest<
  {
    requestBody?: {
      auth_date: string;
      first_name: string;
      hash: string;
      id: number;
      is_luckyspin?: number;
      last_name: string;
      photo_url: string;
      referral_code?: string;
      username: string;
    };
  },
  {}
>("authTelegramRegisterOrLogin", ({ requestBody }) => ({
  url: `/v1/auth/telegram_login`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Ton一键登录-获取Nonce
 */
export const authTonNonce = createRequest<
  {
    requestBody?: { address: string };
  },
  { code: number; data: { nonce: string }; msg: string; request_id: string }
>("authTonNonce", ({ requestBody }) => ({
  url: `/v1/auth/ton_nonce`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Ton一键登录-获取Payload
 */
export const authTonPayload = createRequest<
  {
    requestBody?: {};
  },
  { code: number; data: { payload: string }; msg: string; request_id: string }
>("authTonPayload", ({ requestBody }) => ({
  url: `/v1/auth/ton_payload`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Ton一键注册/登陆
 */
export const authTonRegisterOrLogin = createRequest<
  {
    requestBody?: {
      address: string;
      network: string;
      proof: {
        domain: { lengthBytes: number; value: string };
        payload: string;
        signature: string;
        state_init: string;
        timestamp: number;
      };
      public_key: string;
      referral_code?: string;
    };
  },
  {}
>("authTonRegisterOrLogin", ({ requestBody }) => ({
  url: `/v1/auth/ton_login`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Tron一键登录-获取Nonce
 */
export const authTronNonce = createRequest<
  {
    requestBody?: { address: string };
  },
  { code: number; data: { nonce: string }; msg: string; request_id: string }
>("authTronNonce", ({ requestBody }) => ({
  url: `/v1/auth/tron_nonce`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * Tron一键注册/登陆
 */
export const authTronRegisterOrLogin = createRequest<
  {
    requestBody?: {
      address: string;
      fingerprint: string;
      is_luckyspin?: number;
      referral_code?: string;
      signature: string;
    };
  },
  {}
>("authTronRegisterOrLogin", ({ requestBody }) => ({
  url: `/v1/auth/tron_login`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "application/json", "Service-Name": serviceName },
}));

/**
 * 存款回调
 */
export const callbackDeposit = createRequest<
  {
    requestBody?: string;
  },
  {}
>("callbackDeposit", ({ requestBody }) => ({
  url: `/callback/deposit`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "text/plain", "Service-Name": serviceName },
}));

/**
 * 代付回调
 */
export const callbackGalaxyFiatPayanother = createRequest<
  {
    requestBody?: {
      amount?: number;
      merchant?: string;
      message?: string;
      order_id?: string;
      remark?: string;
      sign?: string;
      status?: number;
    };
  },
  {}
>("callbackGalaxyFiatPayanother", ({ requestBody }) => ({
  url: `/callback/galaxy/fiat_payanother`,
  method: "POST",
  data: requestBody,
  headers: { "Content-Type": "multipart/form-data", "Service-Name": serviceName },
}));

export interface ActivityCheckAvailableBonusesDetail {
  amount: string;
  available: boolean;
  bonuses_details: { balance: string; currency: string; logo: string; name: string }[];
  bonuses_type: number;
  code: string;
  expire_second: number;
  free_game_detail?: {
    bet_amount: string;
    currency: string;
    deposit_amount: string;
    deposit_bet_rate: number;
    game_detail: GameDetail;
    rounds: number;
  };
  name: string;
  next_withdraw: number;
  period: number;
}

export interface AnZhuangPwaHuoDongXiangQing {
  activity_code: string;
  amount: string;
  available: boolean;
  checked: boolean;
  currency: string;
  ename: string;
  expire_second: number;
  period: number;
}

export interface AvatarDetail {
  avatar_id: number;
  level: number;
  path: string;
}

export interface AvatarLevelDetail {
  avatar: null | AvatarDetail[];
  level: number;
  level_icon: string;
  name: string;
}

export interface BiZhongYinHangXiangQing {
  bank_code: string;
  bank_name: string;
  image_icon: string;
  max_withdraw_amount: string;
  min_withdraw_amount: string;
}

export interface BuShiYongJiangJinXiangQing {
  activity_code: string;
  checked: boolean;
  ename: string;
}

export interface CheckInDetail {
  available: boolean;
  bonus_count: number;
  check_in_next_time: number;
  day: number;
  is_checkin: number;
  remaining_bonus_count: number;
}

export interface ChengGong {
  code: number;
  data: string;
  msg: string;
  request_id: string;
}

export interface ClassicDiceBetLog {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  nickname: string;
  nonce: number;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  result: number;
  reward_amount: string;
  rollunder: number;
  seed: GameSeed;
  seed_id: number;
  uid: string;
  user: MyUserDetail;
}

export interface CommissionLogDetail {
  amount: string;
  commissions: number;
  create_time: number;
  currency: string;
  detail?: CurrencyAmountDetail[];
  from_user: null | UserDetailLite;
  game?: null | GameDetail;
  game_code: string;
  log_id: number;
  order_no: string;
  remark: string;
  shared: number;
  status: number;
  type: number;
  valid_amount: string;
  valid_detail?: CurrencyAmountDetail[];
  withdraw_time: number;
}

export interface CrashBetLog {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  game_log: CrashLog;
  game_no: string;
  height: number;
  manual_height: number;
  nickname: string;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  reward_amount: string;
  status: number;
  uid: string;
  user?: MyUserDetail;
}

export interface CrashLog {
  block_no: number;
  client_seed: string;
  crash_time: number;
  create_time: number;
  game_no: string;
  height: number;
  id: number;
  player_count: number;
  result_hash: string;
  server_seed: string;
  status: number;
}

export interface CuXiaoHuoDongXiangQing {
  code: string;
  content: string;
  cover: string;
  create_time: number;
  end_time: number;
  name: string;
  start_time: number;
  status: number;
  type: number;
  url: string;
}

export interface CunKuanFanYongHuoDongXiangQing {
  activity_code: string;
  available: boolean;
  ename: string;
  parent_commissions_rate: number;
  period: number;
  root_commissions_rate: number;
}

export interface CunKuanJiangJinPeiZhi {
  bonus_rate: number;
  deposit_bet_rate: number;
  ename: string;
  max_bonus_amount: string;
  min_deposit_amount: string;
}

export interface CurrencyAmountDetail {
  amount: string;
  currency: string;
}

export interface CurrencyBalance {
  allow_deposit: number;
  allow_withdraw: number;
  balance: string;
  banks?: BiZhongYinHangXiangQing[];
  bonus_balance: string;
  currency: string;
  is_fiat: number;
  locked_balance?: string;
  locked_balance_detail?: SuoDingYuEXiangQing;
  logo: string;
  name: string;
  symbol: string;
  tokens?: CurrencyTokenDetail[];
}

export interface CurrencyCountDetail {
  count: number;
  currency: string;
}

export interface CurrencyDetail {
  allow_deposit: number;
  allow_withdraw: number;
  currency: string;
  is_fiat: number;
  logo: string;
  name: string;
  tokens?: CurrencyTokenDetail[];
  usd_price: string;
  usdt_price: string;
}

export interface CurrencyInfo {
  allow_deposit: number;
  allow_withdraw: number;
  currency: string;
  is_fiat: number;
  logo: string;
  name: string;
  symbol: string;
  usd_price: string;
  usdt_price: string;
}

export interface CurrencyTokenDetail {
  allow_deposit: number;
  allow_withdraw: number;
  block_explorer: string;
  block_query_token_path: string;
  block_query_tx_path: string;
  chain: string;
  code: string;
  contract_address: string;
  currency: string;
  decimal: number;
  is_stable: number;
  max_service_amount: string;
  min_deposit_amount: string;
  min_service_amount: string;
  min_withdraw_amount: string;
  network: string;
  service_amount: string;
  service_amount_rate: string;
}

export interface DaiLiFenHongJiLu {
  actual_profit_amount: string;
  agent_bonus: number;
  amount: string;
  create_time: number;
  currency: string;
  decimal: number;
  expire_time: number;
  last_profit_amount: string;
  level: number;
  order_no: string;
  profit_amount: string;
  remark: string;
  status: number;
  tx_id: string;
  type: number;
  valid_count: number;
  valid_wager: string;
  withdraw_time: number;
}

export interface DepositCommissionLogDetail {
  amount: string;
  bet_amount: string;
  commissions_rate: number;
  create_time: number;
  currency: string;
  deposit_amount: string;
  from_user: null | UserDetailLite;
  log_id: number;
  order_no: string;
  remark: string;
  status: number;
  type: number;
  valid_amount: string;
  withdraw_time: number;
}

export interface DepositDetail {
  amount: string;
  amount_after: string;
  amount_before: string;
  chain: string;
  contract_address: string;
  create_time: number;
  currency?: CurrencyDetail;
  decimal: number;
  from_address: string;
  is_confirmed: number;
  network: string;
  order_no: string;
  pay_time: number;
  remark: string;
  status: number;
  to_address: string;
  transaction_id: string;
  tx_id: string;
}

export interface FanShuiXiangQing {
  bet_amount: number;
  bet_amount_decimals: number;
  bet_content: string;
  bet_valid: number;
  create_time: string;
  game_code: string;
  game_name: string;
  is_commission: number;
  is_settle: number;
  lottery_result: string;
  lottery_result_hash: string;
  pay_confirmed: number;
  pay_status: number;
  pay_time: string;
  prize_status: number;
  rebate_amount: number;
  rebate_amount_decimals: number;
  rebate_ratio: number;
  record_id: number;
  return_amount: number;
  settle_time: string;
  status: number;
  t_amount: number;
  t_amount_decimals: number;
  t_block_hash: string;
  t_block_no: number;
  t_contract_address: string;
  t_contract_type: number;
  t_decimals: number;
  t_from_address: string;
  t_symbol: string;
  t_time: string;
  t_to_address: string;
  t_txid: string;
  user_id: number;
  win_amount: number;
}

export interface FuKuanFangShiXiangQing {
  bank_code: string;
  banks?: YinHangXiangQing[];
  code: string;
  fee: number;
  fee_type: number;
  image_icon: string;
  is_card_no: number;
  is_cpf: number;
  is_maintain: number;
  is_name: number;
  is_phone: number;
  max_deposit_amount: string;
  min_deposit_amount: string;
  name: string;
  quick_options: string[];
}

export interface GameCategoryDetail {
  category_id: number;
  code: string;
  coefficient: number;
  ename: string;
  name: string;
  sort: number;
  status: number;
}

export interface GameDetail {
  code: string;
  default_bet_amount: string;
  ename: string;
  game_provider_detail: GameProviderDetail | null;
  game_type_detail: null | GameTypeDetail;
  image_cover: string;
  image_cover_text: string;
  image_icon: string;
  is_hot: number;
  is_like: number;
  is_top: number;
  like_count: number;
  max_bet_amount: string;
  max_bet_payout: string;
  min_bet_amount: string;
  name: string;
  online_guest_num: number;
  online_user_num: number;
  open_method: number;
  provider_game_id: string;
  provider_id: number;
  status: number;
}

export interface GameProviderDetail {
  alias: string;
  allow_currency: string;
  currencies: string[];
  image_cover: string;
  image_icon: string;
  is_try_available: number;
  name: string;
  provider_code: string;
}

export interface GameSeed {
  client_seed: string;
  create_time: number;
  expiration_time: number;
  id: number;
  max_nonce: number;
  server_seed: string;
  server_seed_hash: string;
  status: number;
  user_id: number;
}

export interface GameTypeDetail {
  alias: string;
  coefficient: number;
  ename?: string;
  game?: null | GameDetail[];
  game_count: number;
  is_expand: number;
  name: string;
  provider?: null | GameProviderDetail[];
  show_position: number;
  type_id: number;
}

export interface GeRenTongZhiXiangQing {
  content: string;
  create_time: number;
  delete_time: number;
  expired_time?: number;
  link_type: number;
  notice_id: number;
  order_no: string;
  read_time: number;
  status: number;
  title: string;
  type: number;
  url: string;
}

export interface GunCunJiLu {
  advance_bet_amount: string;
  amount: string;
  completed_bet_amount: string;
  create_time: number;
  currency: CurrencyDetail;
  deposit_bet_amount: string;
  deposit_bet_rate: number;
  expired_time: number;
  need_bet_amount: string;
  order_no: string;
  status: number;
  type: number;
  withdraw_amount: string;
}

export interface HandshakeDetail {
  adjust_app_token: string;
  app_name: string;
  cf_site_key: string;
  country_currency: string;
  customer_type: number;
  domain: { agent: string; cdn: string; game: string; tab_game: string; verify: string; www: string };
  is_agent: number;
  language: LanguageDetail[];
  login_method: { google: number; metamask: number; telegram: number; ton_connect: number; tronlink: number };
  promotion_channel?: {
    apk_url: string;
    event_install_token: string;
    platform: number;
    pos_id: string;
    use_case: number;
  };
  session_id: string;
  telegram_bot: { username: string; webapp_name: string };
  telegram_business_customer_id: string;
  telegram_channel_id: string;
  telegram_customer_id: string;
  upgrade: number;
  webpush_public_key: string;
}

export interface HashDiceBetLog {
  bet_amount: string;
  chance: number;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  high: number;
  nickname: string;
  nonce: number;
  number: number;
  odds: number;
  order_no: string;
  payout: number;
  prize_status: number;
  profit_amount: string;
  result: number;
  reward_amount: string;
  seed: GameSeed;
  seed_id: number;
  uid: string;
  user: MyUserDetail;
}

export interface HuanYingHuoDongXiangQing {
  activity_code: string;
  available: boolean;
  bonuses_details: { deposit_amount: string; deposit_bonus_rate: number; deposit_boost_rate: number }[];
  boost_expire_second: number;
  deposit_count: number;
  deposit_max_bonus: string;
  ename: string;
  expire_second: number;
  next_deposit: number;
  period: number;
  wager_detail: { deposit_bet_rate: number; max_bet_amount: number; min_bet_amount: number };
}

export interface HuoDongJiangLiLiShiXiangQing {
  activity_amount: string;
  agent_bonus_amount: string;
  bet_amount: string;
  cashback_amount: string;
  check_in_amount: string;
  commission_amount: string;
  deposit_commission_amount: string;
  install_pwa_amount: string;
  invite_reward: string;
  invitespin_amount: string;
  level_upgrade_reward: string;
  rakeback_amount: string;
  total_other_amount: string;
  total_profit_amount: string;
  valid_bet_amount: string;
}

export interface HuoDongJiangLiXiangQing {
  bonuses: ActivityCheckAvailableBonusesDetail[];
  current: number;
  title: string;
  total: number;
}

export interface InviteCircle {
  amount: string;
  currency: string;
  type: number;
}

export interface JieSuanXiangQing {
  amount: number;
  amount_decimals: number;
  create_time: string;
  dateline: number;
  day: number;
  decimals: number;
  id: number;
  modify_time: string;
  month: number;
  order_no: string;
  pay_confirmed: number;
  pay_status: number;
  pay_time: number;
  record_count: number;
  remark: string;
  symbol: string;
  trx_address: string;
  txid: string;
  type: number;
  user_id: number;
  year: number;
  ymd: string;
}

export interface JlYongHuYouXiJiLu {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  game_id: number;
  nickname: string;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  reward_amount: string;
  round_id: string;
  session_id: string;
  transaction_id: string;
  uid: string;
}

export interface LanguageDetail {
  code: string;
  emoji: string;
  id: number;
  is_default: number;
  name: string;
  status: number;
}

export interface LimboBetLog {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  height: number;
  nickname: string;
  nonce: number;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  result: number;
  reward_amount: string;
  seed: GameSeed;
  seed_id: number;
  uid: string;
  user: MyUserDetail;
}

export interface LobbyBetLogDetail {
  bet_amount: string;
  create_time: number;
  currency: string;
  end_time: number;
  game: null | GameDetail;
  game_code: string;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  reward_amount: string;
  status: number;
  user: null | UserDetailLite;
}

export interface LuckySpinCircle {
  amount: string;
  currency: string;
  type: number;
}

export interface MianFeiXuanZhuanXiangQing {
  activity_code: string;
  amount: string;
  available: boolean;
  checked: boolean;
  currency: string;
  deposit_amount: string;
  deposit_max_bonus: string;
  deposit_max_bonus_rate: number;
  ename: string;
  expire_second: number;
  free_game_detail: MianFeiXuanZhuanYouXiXiangQing;
  max_bonus_amount: string;
  period: number;
  wager_detail: { deposit_bet_rate: number; max_bet_amount: number; min_bet_amount: number };
}

export interface MianFeiXuanZhuanYouXiXiangQing {
  bet_amount: string;
  currency: string;
  deposit_amount: string;
  deposit_bet_rate: number;
  game_detail: GameDetail;
  rounds: number;
}

export interface MinesBetLog {
  bet_amount: string;
  cards: string;
  cards_num: number;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  fields: string;
  game: GameDetail;
  mines: number;
  nickname: string;
  nonce: number;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  result: string;
  reward_amount: string;
  seed: GameSeed;
  seed_id: number;
  status: number;
  uid: string;
  user: MyUserDetail;
}

export interface MyUserDetail {
  account: string;
  agent_bonus: number;
  all_ban: number;
  auto_lucky_spin: number;
  avatar: number;
  cashback_time: number;
  currency?: null | CurrencyBalance[];
  deposit_ban: number;
  deposit_time: number;
  email: string;
  email_verified: number;
  enable_authenticator: number;
  eth_address: string;
  is_agent: number;
  is_child_agent: number;
  is_new_user: number;
  is_open_tg_bot: number;
  lang: string;
  last_login_time: number;
  last_login_type: number;
  last_play_time: number;
  last_time: number;
  level: number;
  like_count: number;
  luckyspin_time: number;
  next_level?: null | UserVipLevelDetail;
  nickname: string;
  parent_user?: null | {};
  play_amount: string;
  play_count: number;
  profit_amount: string;
  rakeback_time: number;
  recent_count: number;
  referral_code: string;
  referral_count: number;
  reg_time: number;
  reward_amount: string;
  show_activity: number;
  show_battles_rewards: number;
  show_played_games: number;
  show_profile_data: number;
  show_statistics: number;
  status: number;
  telegram_first_name: string;
  telegram_last_name: string;
  telegram_user_id: number;
  telegram_username: string;
  ton_address: string;
  tron_address: string;
  type: number;
  uid: string;
  unique_id: string;
  user_game_statistics?: null | UserGameStatustics;
  user_game_statistics_list?: null | UserGameStatustics[];
  user_game_top?: null | UserGameTop[];
  user_notice?: null | YongHuTongZhiXiangQing;
  valid_amount: string;
  vip_level: null | UserVipLevelDetail;
  wallet?: null | UserWalletDetail2[];
  withdraw_ban: number;
  withdraw_time: number;
}

export interface Pagination {
  cursor?: number;
  has_more: boolean;
  page: number;
  page_size: number;
  total?: number;
}

export interface PingTaiTongZhiXiangQing {
  content: string;
  covers: string;
  create_time: number;
  is_ad: number;
  is_read: number;
  notice_id: number;
  status: number;
  summary: string;
  title: string;
  url: string;
}

export interface PlinkoBetLog {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  nickname: string;
  nonce: number;
  odds: number;
  order_no: string;
  path: string;
  position: number;
  prize_status: number;
  profit_amount: string;
  reward_amount: string;
  risk: number;
  row: number;
  seed: GameSeed;
  seed_id: number;
  uid: string;
  user: MyUserDetail;
}

export interface PromotionChannelDetail {
  code: string;
  create_time: number;
  id: number;
  name: string;
  url: string;
  visitor_count: number;
}

export interface QianBaoJiLuXiangQing {
  act: number;
  admin_id: number;
  amount: number;
  amount_after: number;
  amount_before: number;
  create_time: string;
  energy_fee: number;
  energy_fee_str: string;
  energy_usage_total: number;
  energy_usage_total_str: string;
  fee: number;
  fee_str: string;
  game_code: string;
  is_confirmed: number;
  log_id: number;
  net_fee: number;
  net_fee_str: string;
  net_usage: number;
  net_usage_str: string;
  order_no: string;
  parent_id?: number;
  receipt_result: string;
  remark: string;
  root_id?: number;
  symbol: string;
  tx_id: string;
  type: number;
  user_id: number;
}

export interface QianBaoXiangQing {
  active_time: string;
  ag_balance: number;
  bet_time: string;
  cash_time: string;
  create_time: string;
  energy_limit: number;
  energy_surplus: number;
  energy_used: number;
  free_net_limit: number;
  free_net_used: number;
  is_actived: number;
  net_limit: number;
  net_surplus: number;
  net_used: number;
  recharge_time: string;
  trx_address: string;
  trx_balance: number;
  trx_frozen_money: number;
  trx_money: number;
  usdt_balance: number;
  usdt_frozen_money: number;
  usdt_money: number;
  user_id: number;
}

export interface QuDaoFanYongFenChengXiangQing {
  category_id: number;
  coefficient: number;
  commissions: number;
  game_id: number;
  game_name: string;
  game_shares?: QuDaoFanYongFenChengXiangQing[];
  is_default: number;
  my_commissions: number;
  shares: number;
}

export interface ReferralUserDetail {
  avatar: number;
  bet_amount: string;
  bonuses_amount: string;
  commission_amount: string;
  deposit_amount: string;
  deposit_time: number;
  last_login_time: number;
  last_play_time: number;
  last_time: number;
  level: number;
  nickname: string;
  parent_channel_id: string;
  parent_channel_name: string;
  parent_channel_referral_code: string;
  parent_channel_telegram_invite_url: string;
  parent_channel_web_invite_url: string;
  play_count: number;
  profit_amount: string;
  referral_count: number;
  reg_time: number;
  status: number;
  team_statistics?: TeamStatisticsDetail;
  uid: string;
  vip_level: UserVipLevelDetail;
}

export interface Seed {
  client_seed: string;
  max_nonce: number;
  server_seed?: string;
  server_seed_hash: string;
}

export interface ShiShiTiXianJiLu {
  amount: string;
  currency: CurrencyDetail;
  nickname: string;
  order_no: string;
}

export interface SlotPeiZhi {
  currencies: string[];
  is_try_available: number;
  name: string;
  provider_alias: string;
  provider_code: string;
  provider_name: string;
}

export interface SuoDingYuEXiangQing {
  completed_bet_amount: string;
  deposit_bet_amount: string;
  status: number;
  withdraw_amount: string;
}

export interface TeamStatisticsDetail {
  currency_statistics: {};
  referral_count: number;
  valuation_statistics: UserCurrencyStatisticsDetail;
}

export interface TelegramBotDetail {
  telegram_bot_enable: number;
  telegram_bot_name: string;
  telegram_bot_token: string;
  telegram_bot_username: string;
  telegram_bot_version: string;
  telegram_bot_webapp_name: string;
  telegram_cs_username: string;
}

export interface TiKuanFangShiXiangQing {
  bank_code: string;
  banks?: YinHangXiangQing[];
  code: string;
  fee: number;
  fee_type: number;
  image_icon: string;
  is_account_type: number;
  is_cpf: number;
  is_email: string;
  is_ifsc_code: number;
  is_maintain: number;
  is_name: number;
  is_phone: number;
  max_withdraw_amount: string;
  min_withdraw_amount: string;
  name: string;
  withdraw_id_type?: { id_type: string; type_name: string }[];
}

export interface TiaoSanPaiHangBangTongJi {
  jackpot_amount: string;
  list: TiaoSanPaiHangBangXiangQing[];
  my_rank?: TiaoSanPaiHangBangXiangQing;
}

export interface TiaoSanPaiHangBangXiangQing {
  bet_amount: string;
  bet_amount_need?: string;
  can_claim?: number;
  rank_no: number;
  reward_rate: string;
  user?: UserDetailLite;
}

export interface TonManifest {
  iconUrl: string;
  name: string;
  privacyPolicyUrl?: string;
  termsOfUseUrl?: string;
  url: string;
}

export interface TowerBetLog {
  bet_amount: string;
  cards: string;
  create_time: number;
  currency: CurrencyBalance;
  difficulty_mode: number;
  end_time: number;
  fields: string;
  game: GameDetail;
  nickname: string;
  nonce: number;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  reward_amount: string;
  seed: GameSeed;
  seed_id: number;
  status: number;
  uid: string;
  user: MyUserDetail;
}

export interface TowerYouXiJiLu {
  account: string;
  bet_amount: number;
  cards: number[][];
  create_time: string;
  difficulty_mode: number;
  end_time: string;
  fields: null | number[];
  nonce: number;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: number;
  reward_amount: number;
  status: number;
  symbol: string;
  user_id: number;
}

export interface TtPayOrderResp {
  expire_second: number;
  out_trade_no: string;
  payment_url: string;
  prepay_id: string;
}

export interface TuiJianGaiLanTongJiXiangQing {
  bet_amount: string;
  channel_id: string;
  click_count: number;
  commission_amount: string;
  commission_shares: QuDaoFanYongFenChengXiangQing[];
  create_time: number;
  deposit_amount: string;
  deposit_count: number;
  is_default: number;
  name: string;
  profit_amount: string;
  referral_code: string;
  referral_user_balance: {
    agent_bonus?: { balance: string };
    commission_rewards: { balance: string; balance_details?: YongHuJiangJinYuEXiangQing[] };
    deposit_commission_rewards: {
      balance: string;
      balance_details?: YongHuJiangJinYuEXiangQing[];
      locked_balance: string;
    };
    referral_bonus: { balance: string; locked_balance: string };
  };
  reg_count: number;
  telegram_invite_url: string;
  web_invite_url: string;
}

export interface TuiJianJiangJinGuiZeXiangQing {
  referral_bonus: string;
  referral_wager: string;
  referrer_bonus: string;
}

export interface UltimateDiceBetLog {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  high: number;
  low: number;
  nickname: string;
  nonce: number;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  result: number;
  reward_amount: string;
  seed: GameSeed;
  seed_id: number;
  uid: string;
  user: MyUserDetail;
}

export interface UserCashbackDetail {
  amount: string;
  amounts: CurrencyAmountDetail[];
  create_time: number;
  decimal: number;
  expire_time: number;
  order_no: string;
  remark: string;
  status: number;
  tx_id: string;
  withdraw_time: number;
}

export interface UserCurrencyDetail {
  balance: string;
  create_time: number;
  currency: string;
  decimal: number;
  frozen_balance: string;
  modify_time: number;
  user_id: number;
  withdraw_time: number;
}

export interface UserCurrencyStatisticsDetail {
  activity_amount: string;
  actual_profit_amount: string;
  agent_bonus_amount: string;
  balance?: string;
  bet_amount: string;
  bet_count: number;
  bonus_amount: string;
  bonus_coefficient: number;
  cashback_amount: string;
  check_in_amount: string;
  commission_amount: string;
  currency: string;
  deposit_amount: string;
  deposit_commission_amount: string;
  install_pwa_amount: string;
  invite_reward: string;
  invitespin_amount: string;
  level_upgrade_reward: string;
  profit_amount: string;
  rakeback_amount: string;
  reward_amount: string;
  total_profit_amount: string;
  valid_bet_amount: string;
  win_count: number;
  withdraw_amount: string;
}

export interface UserDailyReportDetail {
  activity_amount: string;
  activity_amount_detail: null | CurrencyAmountDetail[];
  agent_bonus_amount: string;
  agent_bonus_amount_detail: null | CurrencyAmountDetail[];
  bet_amount: string;
  bet_amount_detail: null | CurrencyAmountDetail[];
  bet_count: number;
  bet_count_detail: null | CurrencyCountDetail[];
  bonus_amount: string;
  bonus_amount_detail: null | CurrencyAmountDetail[];
  cashback_amount: string;
  cashback_amount_detail: null | CurrencyAmountDetail[];
  check_in_amount: string;
  commission_amount: string;
  commission_amount_detail: null | CurrencyAmountDetail[];
  currency: string;
  dateline: number;
  deposit_amount: string;
  deposit_amount_detail: null | CurrencyAmountDetail[];
  deposit_count: number;
  invite_reward: string;
  level_upgrade_reward: string;
  modify_time: number;
  profit_amount: string;
  profit_amount_detail: null | CurrencyAmountDetail[];
  rakeback_amount: string;
  rakeback_amount_detail: null | CurrencyAmountDetail[];
  reward_amount: string;
  reward_amount_detail: null | CurrencyAmountDetail[];
  total_profit_amount: string;
  total_profit_amount_detail: null | CurrencyAmountDetail[];
  win_count: number;
  win_count_detail: null | CurrencyCountDetail[];
  withdraw_amount: string;
  withdraw_amount_detail: null | CurrencyAmountDetail[];
  withdraw_count: number;
}

export interface UserDetail {
  avatar: number;
  cashback_time: number;
  deposit_time: number;
  last_login_time: number;
  last_play_time: number;
  last_time: number;
  level: number;
  luckyspin_time: number;
  nickname: string;
  play_count: number;
  rakeback_time: number;
  reg_time: number;
  show_activity: number;
  show_battles_rewards: number;
  show_played_games: number;
  show_profile_data: number;
  show_statistics: number;
  uid: string;
  user_game_statistics?: null | UserGameStatustics;
  user_game_top?: null | UserGameTop[];
  vip_level: UserVipLevelDetail | null;
  withdraw_time: number;
}

export interface UserDetailLite {
  avatar: number;
  level: number;
  nickname: string;
  uid: string;
}

export interface UserGameLog {
  bet_amount: string;
  create_time: number;
  currency?: null | CurrencyBalance;
  end_time: number;
  game?: null | GameDetail;
  game_code: string;
  game_no: string;
  odds: number;
  order_no: string;
  prize_status: number;
  profit_amount: string;
  reward_amount: string;
  status: number;
  user?: null | UserDetail;
}

export interface UserGameStatustics {
  bet_amount: string;
  bet_count: number;
  currency: string;
  logo?: string;
  profit_amount?: string;
  reward_amount: string;
  win_count: number;
}

export interface UserGameTop {
  bet_amount: string;
  bet_count: number;
  currency: string;
  game: GameDetail;
  game_code: string;
  win_count: number;
}

export interface UserInviteChannelDetail {
  bet_amount: string;
  channel_id: string;
  click_count: number;
  commission_amount: string;
  commission_shares: QuDaoFanYongFenChengXiangQing[];
  create_time: number;
  deposit_amount: string;
  deposit_count: number;
  ftd_amount: string;
  ftd_count: number;
  is_default: number;
  name: string;
  profit_amount: string;
  referral_code: string;
  reg_count: number;
  std_count: number;
  telegram_invite_url: string;
  web_invite_url: string;
}

export interface UserSessionDetail {
  browser_languages?: string;
  browser_name: string;
  browser_platform?: string;
  browser_vendor?: string;
  browser_vendor_flavors?: string;
  browser_version: string;
  browser_visitor_id?: string;
  create_time: number;
  data: string;
  device: string;
  expire_time: number;
  is_me: number;
  last_device: string;
  last_ip: string;
  last_ip_address: string;
  last_time: number;
  login_device: string;
  login_ip: string;
  login_ip_address: string;
  modify_time: number;
  os: string;
  os_version: string;
  platform: string;
  screen_resolution?: string;
  session_id: string;
  timezone?: string;
  user_agent: string;
}

export interface UserVipLevelDetail {
  bet_multiples: number;
  cashback_rate: number;
  deposit_back_rate: number;
  level: number;
  level_icon: string;
  name: string;
  rakeback_rate: number;
  referral_rate: number;
  status?: number;
  total_wager: number;
  upgrade_reward: string;
}

export interface UserWalletDetail {
  active_time: number;
  balance: string;
  chain: string;
  collect_time: number;
  create_time: number;
  deposit_time: number;
  is_activated: number;
  is_add_3rd: number;
  transfer_time: number;
  user_id: number;
  wallet_address: string;
}

export interface UserWalletDetail1 {
  balance: string;
  chain: string;
  deposit_time: number;
  wallet_address: string;
}

export interface UserWalletDetail2 {
  active_time: number;
  balance: string;
  chain: string;
  collect_time: number;
  create_time: number;
  deposit_time: number;
  is_activated: number;
  is_add_3rd: number;
  transfer_time: number;
  user_id: number;
  wallet_address: string;
}

export interface VideoPokerYouXiJiLu {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  game_no: string;
  init_player_cards: { s: string; v: string }[];
  nonce: number;
  odds: number;
  order_no: string;
  player_cards: { s: string; v: string }[];
  prize_status: number;
  profit_amount: string;
  result: number;
  reward_amount: string;
  seed: { client_seed: string; max_nonce: number; server_seed_hash: string };
  user: null;
}

export interface WheelBetLog {
  bet_amount: string;
  create_time: number;
  currency: CurrencyBalance;
  end_time: number;
  game: GameDetail;
  nickname: string;
  nonce: number;
  odds: number;
  order_no: string;
  position: number;
  prize_status: number;
  profit_amount: string;
  reward_amount: string;
  risk: number;
  seed: GameSeed;
  seed_id: number;
  segment: number;
  uid: string;
  user: MyUserDetail;
}

export interface WithdrawDetail {
  amount: string;
  amount_after: string;
  amount_before: string;
  audit_time: number;
  chain: string;
  create_time: number;
  currency: string;
  currency_code: string;
  finish_time: number;
  from_address: string;
  handle_status: number;
  handle_time: number;
  network: string;
  order_no: string;
  pay_time: number;
  remark: string;
  show_reason: string;
  show_status: number;
  to_address: string;
  tx_id: string;
  withdraw_status: number;
}

export interface XiaoHuoJianTouZhuJiLu {
  account: string;
  bet_amount: number;
  create_time: number;
  end_time: number;
  game_no: string;
  height: number;
  log_id: number;
  manual_height: number;
  order_no: string;
  prize_status: number;
  profit_amount: number;
  reward_amount: number;
  status: number;
  symbol: string;
  user_id: number;
}

export interface XiaoHuoJianTouZhuJiLuWs {
  account: string;
  auto: number;
  bet_amount: number;
  crash_height: number;
  prize_status: number;
  profit: number;
  reward: number;
  reward_height: number;
  symbol: string;
  time: number;
}

export interface XiaoHuoJianYouXiJiLu {
  block_no: number;
  client_seed: string;
  crash_time: string;
  game_no: string;
  height: number;
  id: number;
  result_hash: string;
  server_seed: string;
}

export interface XiaoHuoJianYouXiJiLuWs {
  account: string;
  auto: number;
  bet_amount: number;
  crash_height: number;
  prize_status: number;
  profit: number;
  reward: number;
  reward_height: number;
  symbol: string;
  time: number;
}

export interface YaoQingGuanXiXiangQing {
  create_time: number;
  help_status: number;
  help_time: number;
  nickname: string;
}

export interface YaoQingHuoDongJianChaKeYong {
  amount: string;
  available: boolean;
  count: number;
  currency: string;
  expire_second: number;
  expire_time: number;
  max_amount: string;
  max_bonuses: string;
  next_withdraw: number;
  next_withdraw_second: number;
  order_no: string;
  period: number;
  withdraw_amount: string;
  withdraw_status: number;
}

export interface YaoQingZhuanPanJiLu {
  amount: string;
  create_time: number;
  currency: {
    allow_deposit: number;
    allow_play: number;
    allow_withdraw: number;
    balance: string;
    bonus_balance: string;
    currency: string;
    is_fiat: number;
    locked_balance: string;
    logo: string;
    name: string;
    symbol: string;
  };
  decimal: number;
  expired_time: number;
  order_no: string;
  status: number;
  withdraw_amount: string;
}

export interface YinHangXiangQing {
  bank_code: string;
  bank_name: string;
  is_default: number;
}

export interface YongHuDaiLiDengJiXiangQing {
  agent_bonus: number;
  id: number;
  level: number;
  level_icon: string;
  name: string;
  status: number;
  total_profit: string;
  valid_count: number;
  valid_wager: string;
}

export interface YongHuFanYongShuaiXiangQing {
  category_id: number;
  coefficient: number;
  commissions: number;
  game_commissions?: YongHuFanYongShuaiXiangQing[];
  game_id: number;
  game_name: string;
  max_commissions: number;
  min_commissions: number;
}

export interface YongHuJiangJinYuEXiangQing {
  balance: string;
  create_time: number;
  currency: string;
  decimal: number;
  locked_balance: string;
  logo: string;
  modify_time: number;
  type: number;
  withdraw_time: number;
}

export interface YongHuTongZhiXiangQing {
  notice_count: number;
  personal_count: number;
  platform_count: number;
}

export interface YongJinXiangQing {
  bet_amount: number;
  bet_amount_decimals: number;
  bet_content: string;
  bet_valid: number;
  create_time: string;
  game_code: string;
  game_name: string;
  is_settle: number;
  level: number;
  lottery_result: string;
  lottery_result_hash: string;
  parent_amount: number;
  parent_amount_decimals: number;
  parent_id: number;
  parent_pay_confirmed: number;
  parent_pay_status: number;
  parent_pay_time: string;
  prize_status: number;
  record_id: number;
  return_amount: number;
  root_amount: number;
  root_amount_decimals: number;
  root_id: number;
  root_pay_confirmed: number;
  root_pay_status: number;
  root_pay_time: string;
  settle_time: number;
  status: number;
  t_amount: number;
  t_amount_decimals: number;
  t_block_hash: string;
  t_block_no: number;
  t_contract_address: string;
  t_contract_type: number;
  t_decimals: number;
  t_from_address: string;
  t_symbol: string;
  t_time: string;
  t_to_address: string;
  t_txid: string;
  user?: MyUserDetail;
  user_id: number;
  win_amount: number;
}

export interface ZhangBianJiLu {
  act: number;
  amount: string;
  amount_after: string;
  amount_before: string;
  create_time: number;
  currency?: null | CurrencyBalance;
  decimal: number;
  deposit?: null | DepositDetail;
  from_user?: null | UserDetail;
  game?: null | GameDetail;
  game_code: string;
  order_no: string;
  remark: string;
  tx_id: string;
  type: number;
  withdraw?: null | WithdrawDetail;
}

export interface ZhuCeHuoDongXiangQing {
  activity_code: string;
  amount: string;
  available: boolean;
  checked: boolean;
  currency: string;
  deposit_amount: string;
  deposit_max_bonus: string;
  ename: string;
  expire_second: number;
  period: number;
  wager_detail: { deposit_bet_rate: number; max_bet_amount: number; min_bet_amount: number };
}

export interface ZhuanZhangJiLu {
  amount: string;
  create_time: number;
  currency: string;
  order_no: string;
  remark: string;
  to_user: null | UserDetail;
  type: number;
}
