<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { icons, useJackpotData } from '../useJackpotData';

const { subjectHistoryList, subjectHistory } = useJackpotData();

// 滚动同步逻辑
const leftTableContent = ref<HTMLElement>();
const rightTableContent = ref<HTMLElement>();
let isScrolling = false;

// 同步滚动函数
function syncScroll(sourceElement: HTMLElement, targetElement: HTMLElement) {
  if (isScrolling) return;

  isScrolling = true;
  targetElement.scrollTop = sourceElement.scrollTop;

  // 使用 requestAnimationFrame 确保滚动完成后重置标志
  requestAnimationFrame(() => {
    isScrolling = false;
  });
}

// 设置滚动监听
onMounted(async () => {
  await nextTick();

  if (leftTableContent.value && rightTableContent.value) {
    // 左表格滚动时同步右表格
    leftTableContent.value.addEventListener('scroll', () => {
      if (rightTableContent.value) {
        syncScroll(leftTableContent.value!, rightTableContent.value);
      }
    });

    // 右表格滚动时同步左表格
    rightTableContent.value.addEventListener('scroll', () => {
      if (leftTableContent.value) {
        syncScroll(rightTableContent.value!, leftTableContent.value);
      }
    });
  }
});
</script>

<template>
  <!-- history -->
  <div class="jackpot-subject-history">
    <p class="title lt-tablet:hidden">
      History
    </p>
    <div class="rounded-lg bg-sys-layer-a px-7.5 py-3.5 text-center text-sys-text-body">
      <p class="text-base">
        Jackpot
      </p>
      <h5 class="text-5 text-#18A349">
        <TNum :value="(subjectHistory?.jackpot_amount ?? 0)" :decimals="2" format="original" />
      </h5>
    </div>
    <div class="twins-table twins-table-double">
      <div class="flex-1">
        <div class="twins-table_title">
          <span class="text-left">Ranking</span>
          <span>Username</span>
          <span class="text-right">Bet Volume</span>
        </div>
        <div class="twins-table_content">
          <template v-for="(i, k) of subjectHistoryList[0]" :key="k">
            <div class="text-left">
              <img v-if="Number(i.rank_no <= 3)" :src="icons[i.rank_no - 1]" alt="" class="w-4 object-cover">
              <span v-else>{{ i.rank_no }}</span>
            </div>
            <div>{{ i.user?.nickname }}</div>
            <div class="text-right">
              <TNum :value="(i?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
            </div>
          </template>
        </div>
      </div>
      <div />
      <div class="flex-1">
        <div class="twins-table_title">
          <span class="text-left">Ranking</span>
          <span>Username</span>
          <span class="text-right">Bet Volume</span>
        </div>
        <div class="twins-table_content">
          <template v-for="(i, k) of subjectHistoryList[1]" :key="k">
            <div class="text-left">
              <img v-if="Number(i.rank_no <= 3)" :src="icons[i.rank_no - 1]" alt="" class="w-4 object-cover">
              <span v-else>{{ i.rank_no }}</span>
            </div>
            <div>{{ i.user?.nickname }}</div>
            <div class="text-right">
              <TNum :value="(i?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="aggregate">
      <div class="aggregate-left">
        {{ subjectHistory.my_rank?.rank_no ?? 0 }}
      </div>
      <div class="divider-v-gradual" />
      <div class="aggregate-center">
        <p>My Bets: {{ subjectHistory?.my_rank?.bet_amount ?? 0 }}</p>
        <p>My Rewards: <TNum :value="((subjectHistory?.my_rank?.reward_rate ?? 0) * 100)" :decimals="2" format="original" /> %</p>
      </div>
      <div class="divider-v-gradual" />
      <div class="aggregate-rigth">
        <p>Ranks Left</p>
        <p>{{ subjectHistory?.my_rank?.bet_amount_need ?? 0 }}</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '../style';
</style>
