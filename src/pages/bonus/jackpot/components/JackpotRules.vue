<script setup lang="ts">
import { icons, useJackpotData } from '../useJackpotData';

const { rulesConfigData } = useJackpotData();
const rules = ref(() => {
  return {
    activity_rules: [
      'Players\' rankings on the daily leaderboard, weekly leaderboard, and overall ranking are based on their corresponding total bet amount.',
      'Rewards will be displayed on the leaderboard event page.',
      'The daily leaderboard is calculated from 00:00 to 24:00 local time. Daily rewards will be issued within half an hour after the end of the daily leaderboard and can be claimed manually on the promotions page.',
      'The weekly ranking is calculated from 00:00 local time on Monday to 24:00 local time on Sunday. Weekly rewards will be issued within half an hour after the end of the weekly cycle and can be claimed manually on the promotions page.',
    ],
    teems_rules: [
      'Before applying for rewards, players must provide their phone number and bank account information. The phone number, IP address, and bank account information must match the region and personal data. Any abnormal behavior by the player will result in the cancellation of their bonuses or earnings.',
      'Players who open multiple accounts or fraudulent accounts will lose their eligibility to participate in events due to improper behavior on the platform, and the related accounts will be frozen.',
      'Please be sure to read these terms and conditions carefully before participating in this event.',
    ],
  };
});
</script>

<template>
  <!-- Rules -->
  <div class="jackpot-subject-rules">
    <p class="title lt-table:hidden">
      Rules
    </p>
    <div class="twins-table">
      <div class="min-w-80 lt-tablet:min-w-auto">
        <div class="flex-between text-sys-text-body">
          <span class="text-left">Ranking</span>
          <span class="text-right">Bet Volume</span>
        </div>
        <div class="twins-table-main max-h-460px overflow-y-auto">
          <template v-for="(i, k) of rulesConfigData" :key="k">
            <div class="text-left">
              <img v-if="Number(i.rank <= 3)" :src="icons[i.rank - 1]" alt="" class="w-4 object-cover">
              <span v-else>{{ i.rank }}</span>
            </div>
            <div class="text-right">
              <TNum :value="((i?.reward_rate ?? 0) * 100)" :decimals="2" format="original" /> %
            </div>
          </template>
        </div>
      </div>
      <div class="divider-v-gradual h-auto" />
      <div class="text-left">
        <div class="text-center text-24px text-white font-900 font-normal tablet:hidden">
          Event Rules
        </div>
        <div class="text-sys-text-body">
          <h4 class="text-sys-text-head">
            Activity rules
          </h4>

          <div v-for="(i, k) in rules().activity_rules" :key="k" class="rule-item px-2 py-0.5 text-sm text-sys-text-body">
            {{ `${k + 1}. ${i}` }}
          </div>
        </div>
        <div class="mt-5.5 text-sys-text-body">
          <h4 class="text-sys-text-head">
            Terms Conditions
          </h4>
          <div v-for="(i, k) in rules().teems_rules" :key="k" class="rule-item px-2 py-0.5 text-sm text-sys-text-body">
            {{ `${k + 1}. ${i}` }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '../style';
.jackpot-subject-rules {
  .twins-table {
    --uno: 'grid grid-cols-[auto_auto_1fr] w-full items-stretch gap-10';
  }
  @media bp-lt-tablet {
    .twins-table {
      --uno: 'grid-cols-1';
    }
  }

  .rule-item {
    text-indent: -1.4em; /* 首行负缩进 */
    padding-left: 1.6em; /* 左边距补偿 */
    line-height: 1.6; /* 增加行高以便更好地显示多行文本 */
  }
}
</style>
