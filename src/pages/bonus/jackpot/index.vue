<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { watchDeep } from '@peng_kai/kit/libs/vueuse';
import JackpotHeader from '~/pages/bonus/jackpot/components/JackpotHeader.vue';
import JackpotHistory from '~/pages/bonus/jackpot/components/JackpotHistory.vue';
import JackpotRules from '~/pages/bonus/jackpot/components/JackpotRules.vue';

import { useJackpotData } from './useJackpotData';

const { subjectLiveList, subjectLive } = useJackpotData();

// 滚动同步逻辑
const leftTableContent = ref<HTMLElement>();
const rightTableContent = ref<HTMLElement>();
let isScrolling = false;

// 同步滚动函数
function syncScroll(sourceElement: HTMLElement, targetElement: HTMLElement) {
  if (isScrolling) return;

  isScrolling = true;
  targetElement.scrollTop = sourceElement.scrollTop;

  // 使用 requestAnimationFrame 确保滚动完成后重置标志
  requestAnimationFrame(() => {
    isScrolling = false;
  });
}

// 设置滚动监听
onMounted(async () => {
  await nextTick();

  if (leftTableContent.value && rightTableContent.value) {
    // 左表格滚动时同步右表格
    leftTableContent.value.addEventListener('scroll', () => {
      if (rightTableContent.value) {
        syncScroll(leftTableContent.value!, rightTableContent.value);
      }
    });

    // 右表格滚动时同步左表格
    rightTableContent.value.addEventListener('scroll', () => {
      if (leftTableContent.value) {
        syncScroll(rightTableContent.value!, leftTableContent.value);
      }
    });
  }
});

const history = useAntdModal(
  JackpotHistory,
  {
    title: computed(() => $t('fWgSgW1tklRsPc5ObejY')),
    wrapClassName: 'ant-cover__Modal-drawer [--min-modal-height:70vh]',
    centered: true,
    footer: null,
  },
);
const rules = useAntdModal(
  JackpotRules,
  {
    title: computed(() => $t('fWgSgW1tklRsPc5ObejY')),
    wrapClassName: 'ant-cover__Modal-drawer [--min-modal-height:90vh]',
    centered: true,
    footer: null,
  },
);

const bp = useAppBreakpoints();
watchDeep(bp, () => {
  bp.tablet && history.close?.();
  bp.tablet && rules.close?.();
});
</script>

<template>
  <div class="jackpot">
    <JackpotHeader />
    <!-- router -->
    <div class="jackpot-router">
      <div @click="rules?.open?.()">
        Rules
      </div>
      <div @click="history?.open?.()">
        History
      </div>
    </div>
    <!-- subject -->
    <div class="jackpot-subject">
      <!-- 排行 -->
      <div class="jackpot-subject-ranking">
        <div class="jackpot-subject-ranking_top mb-10 lt-tablet:(mx--5)">
          <img src="../imgs/jackpot-ranking.png" class="object-cover">
          <div class="copy">
            <div class="mb-2">
              {{ subjectLiveList[0][0]?.user.nickname }}
              <br>
              <TNum :value="(subjectLiveList[0][0]?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
            </div>
            <div class="mb-6">
              {{ subjectLiveList[1][0]?.user?.nickname }}
              <br>
              <TNum :value="(subjectLiveList[1][0]?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
            </div>
            <div class="mb-1">
              {{ subjectLiveList[0][1]?.user?.nickname }}
              <br>
              <TNum :value="(subjectLiveList[0][1]?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
            </div>
          </div>
        </div>
        <div class="twins-table twins-table-double">
          <div class="twins-table-double_left">
            <div class="twins-table_title">
              <span class="text-left">Ranking</span>
              <span>Username</span>
              <span class="text-right">Bet Volume</span>
            </div>
            <div class="twins-table_content">
              <template v-for="i of subjectLiveList[1]" :key="i">
                <template v-if="i.rank_no > 3">
                  <div class="text-left">
                    {{ i.rank_no }}
                  </div>
                  <div>{{ i.user?.nickname }}</div>
                  <div class="text-right">
                    <TNum :value="(i?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
                  </div>
                </template>
              </template>
            </div>
          </div>
          <div />
          <div class="twins-table-double_right">
            <div class="twins-table_title">
              <span class="text-left">Ranking</span>
              <span>Username</span>
              <span class="text-right">Bet Volume</span>
            </div>
            <div class="twins-table_content">
              <template v-for="i of subjectLiveList[0]" :key="i">
                <template v-if="i.rank_no > 3">
                  <div class="text-left">
                    {{ i.rank_no }}
                  </div>
                  <div>{{ i.user?.nickname }}</div>
                  <div class="text-right">
                    <TNum :value="(i?.bet_amount ?? 0)" :decimals="2" format="fixed-dec" />
                  </div>
                </template>
              </template>
            </div>
          </div>
        </div>
        <div class="aggregate">
          <div class="aggregate-left">
            {{ subjectLive.my_rank?.rank_no }}
          </div>
          <div class="divider-v-gradual" />
          <div class="aggregate-center">
            <p>My Bets: {{ subjectLive?.my_rank?.bet_amount }}</p>
            <p>My Rewards: <TNum :value="((subjectLive?.my_rank?.reward_rate ?? 0) * 100)" :decimals="2" format="original" /> %</p>
          </div>
          <div class="divider-v-gradual" />
          <div class="aggregate-rigth">
            <p>Ranks Left</p>
            <p>{{ subjectLive?.my_rank?.bet_amount_need }}</p>
          </div>
        </div>
      </div>
      <template v-if="$bp.tablet">
        <JackpotHistory />
        <JackpotRules />
      </template>
    </div>

    <history.PresetComponent />
    <rules.PresetComponent />
  </div>
</template>

<style>
@import 'style.scss';
</style>

<style lang="scss" scoped>
.jackpot-subject-ranking {
  .jackpot-subject-ranking_top {
    --uno: 'relative inline-block self-center max-w-auto';

    img {
      width: clamp(350px, 80.583vw, 860px);
    }

    .copy {
      --uno: 'absolute w-full h-[calc(100%_-_0.4vw)] flex justify-around top-0 left-0 items-end text-14px lt-tablet:text-xs';
    }
  }
  @media bp-lt-tablet {
    --uno: 'mt-0';
  }
}
</style>
