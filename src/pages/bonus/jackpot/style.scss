.jackpot {
  --uno: 'flex-between flex-col gap-15 lt-tablet:gap-5';
}
.jackpot-router {
  --uno: 'absolute *:rounded-r-lg *:bg-sys-layer-a/70 *:px-5 *:py-7px *:text-sm *:text-white *:cursor-pointer top-13 flex flex-col gap-2.5 tablet:hidden';
  left: calc(-1 * var(--main-base-margin));

}

// ===== 主要内容区域样式 =====
.jackpot-subject {
  --uno: 'page-content-w flex-start flex-col items-stretch gap-15 lt-tablet:px-2';
}

// ===== 通用子组件样式 =====
.jackpot-subject-ranking,
.jackpot-subject-history,
.jackpot-subject-rules {
  --uno: 'flex flex-col gap-10 text-center gap-5';

  // 标题样式
  .title {
    --uno: 'text-8 font-900 leading-normal';
  }

  // 双表格样式
  .twins-table {
    --uno: 'flex-between items-stretch gap-80px lt-tablet:(text-sm)';

    &.twins-table-double{
      --uno: 'h-460px lt-tablet:h-360px';

      > div:nth-child(1),
      > div:nth-child(3) {
        --uno: 'flex-1 flex flex-col';

        .twins-table_title {
          --uno: 'grid grid-cols-3 text-sys-text-body mb-2 flex-shrink-0 sticky top-0 bg-sys-layer-b z-10 py-2';
        }

        .twins-table_content {
          --uno: 'grid grid-cols-3 flex-1 overflow-y-auto';
          scroll-behavior: smooth;
        }
      }
    }

    .twins-table-main{
      --uno: 'grid grid-cols-2 mt-2';
    }
   .twins-table-double_left,
     .twins-table-double_right{
      --uno: 'flex-1 relative';

      .twins-table_title {
        --uno: 'grid grid-cols-3 text-sys-text-body mb-2 absolute top--8 w-100% z-1';
      }

      .twins-table_content {
        --uno: 'grid grid-cols-3 ';
      }
    }

    > div:nth-child(2) {
      --uno: 'divider-v-gradual h-auto';
    }

    > div:nth-child(3),> div:nth-child(2) {
      --uno: 'lt-tablet:hidden';
    }
  }

  // 聚合信息样式
  .aggregate {
    --uno: 'flex-between rounded-lg from-#18A349 to-#E9B308 bg-gradient-to-r p-7.5 text-white lt-tablet:(rounded-4 p-4 text-sm)';
    .aggregate-left{
      --uno: 'min-w-60 text-left';
    }
    .aggregate-center{
      --uno: 'text-left';
    }
    .aggregate-rigth{
      --uno: 'text-right';
    }
    .divider-v-gradual{
      --uno: 'h-auto';
    }
    @media bp-lt-tablet {
      .aggregate-left,.aggregate-rigth{
        --uno: 'min-w-auto';
      }
    }
  }
}

